# Created at 2025-06-12T11:09:37.439
Exception in thread "main" java.lang.reflect.InvocationTargetException

# Created at 2025-06-12T11:09:37.439
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)

# Created at 2025-06-12T11:09:37.440
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)

# Created at 2025-06-12T11:09:37.440
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)

# Created at 2025-06-12T11:09:37.440
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)

# Created at 2025-06-12T11:09:37.442
	at java.instrument/sun.instrument.InstrumentationImpl.loadClassAndStartAgent(InstrumentationImpl.java:491)

# Created at 2025-06-12T11:09:37.442
	at java.instrument/sun.instrument.InstrumentationImpl.loadClassAndCallPremain(InstrumentationImpl.java:503)

# Created at 2025-06-12T11:09:37.442
Caused by: java.lang.RuntimeException: Class java/lang/UnknownError could not be instrumented.

# Created at 2025-06-12T11:09:37.442
	at org.jacoco.agent.rt.internal_28bab1d.core.runtime.ModifiedSystemClassRuntime.createFor(ModifiedSystemClassRuntime.java:140)

# Created at 2025-06-12T11:09:37.444
	at org.jacoco.agent.rt.internal_28bab1d.core.runtime.ModifiedSystemClassRuntime.createFor(ModifiedSystemClassRuntime.java:101)

# Created at 2025-06-12T11:09:37.444
	at org.jacoco.agent.rt.internal_28bab1d.PreMain.createRuntime(PreMain.java:55)

# Created at 2025-06-12T11:09:37.444
	at org.jacoco.agent.rt.internal_28bab1d.PreMain.premain(PreMain.java:47)

# Created at 2025-06-12T11:09:37.444
	... 6 more

# Created at 2025-06-12T11:09:37.444
Caused by: java.lang.NoSuchFieldException: $jacocoAccess

# Created at 2025-06-12T11:09:37.445
	at java.base/java.lang.Class.getField(Class.java:2117)

# Created at 2025-06-12T11:09:37.445
	at org.jacoco.agent.rt.internal_28bab1d.core.runtime.ModifiedSystemClassRuntime.createFor(ModifiedSystemClassRuntime.java:138)

# Created at 2025-06-12T11:09:37.445
	... 9 more

# Created at 2025-06-12T11:09:37.445
*** java.lang.instrument ASSERTION FAILED ***: "result" with message agent load/premain call failed at s\src\java.instrument\share\native\libinstrument\JPLISAgent.c line: 422

