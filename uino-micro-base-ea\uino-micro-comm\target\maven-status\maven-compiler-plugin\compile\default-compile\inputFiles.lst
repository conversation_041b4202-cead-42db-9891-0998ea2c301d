D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-comm\src\main\java\com\binary\framework\web\ErrorCode.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-comm\src\main\java\com\binary\framework\web\RemoteResult.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-comm\src\main\java\com\uino\comm\exception\FrameworkException.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-comm\src\main\java\com\uino\comm\web\feign\AbsFeignServerControllerAdvice.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-comm\src\main\java\com\uino\comm\exception\UinoBasicException.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-comm\src\main\java\com\uino\comm\feign\exception\FeignInterfaceException.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-comm\src\main\java\com\uino\comm\feign\FeignErrorDecoder.java
