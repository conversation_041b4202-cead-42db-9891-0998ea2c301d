D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\permission\ModuleFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\DataSetExeResultFegin.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\monitor\AlarmFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\permission\UserFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\sys\TenantDomainFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\DirFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\sys\OperateLogModuleFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\ImageFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\DataSetCooperationFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\DataSetMallApiLogFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\CiRltAutoBuildFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\monitor\SimulationRuleFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\RltRuleFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\sys\ResourceFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\sys\LogFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\TagFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\RltClassFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\permission\OauthFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\TopDataFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\sys\SysFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\CIClassFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\CIHistoryFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\CIFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\TaskLockFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\monitor\UinoPerformanceFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\permission\OrgFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\license\LicenseAuthFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\monitor\KpiFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\CITreeFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\CIRltFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\DataSetTopFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\CIClassRltFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\monitor\MonSeverityFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\sys\CIOperateLogFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\sys\NotifyChannelFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\sys\TableDataConfigFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\sys\LoginAuthConfigFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\DataSetFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\permission\RoleFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\plugin\PluginFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\monitor\PerformanceFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\GraphAnalysisFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\sys\OperateLogFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\sys\LogoFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\DataSetPriorityDisplayFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\sys\DictionaryFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\VisualModelFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\permission\ButtonFeign.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\cmdb\RelationRuleAnalysisFegin.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\config\BaseFeignConfig.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-feign-client\src\main\java\com\uino\provider\feign\monitor\UinoEventFeign.java
