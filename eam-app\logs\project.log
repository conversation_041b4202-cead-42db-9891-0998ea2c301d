2025-06-12 11:14:03.780 INFO [main] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-12 11:14:04.111 INFO [restartedMain] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-12 11:14:04.717 INFO [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback12.LogbackNacosLoggingAdapterBuilder
2025-06-12 11:14:04.718 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-12 11:14:04.719 INFO [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback14.LogbackNacosLoggingAdapterBuilder
2025-06-12 11:14:04.721 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-12 11:14:04.721 INFO [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.log4j2.Log4j2NacosLoggingAdapterBuilder
2025-06-12 11:14:04.722 INFO [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Nacos Logging Adapter: com.alibaba.nacos.logger.adapter.log4j2.Log4J2NacosLoggingAdapter match org.apache.logging.slf4j.Log4jLogger success.
2025-06-12 11:14:04.955 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Starting EamApplication using Java 17.0.4.1 with PID 44616 (D:\workspace\mycode\ea\eam-app\eam-web\target\classes started by lichong in D:\workspace\mycode\ea\eam-app)
2025-06-12 11:14:04.962 INFO [restartedMain] com.uinnova.product.eam.EamApplication : The following 1 profile is active: "local"
2025-06-12 11:14:05.272 INFO [restartedMain] org.springframework.boot.devtools.env.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-12 11:14:05.272 INFO [restartedMain] org.springframework.boot.devtools.env.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-12 11:14:08.398 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.399 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.401 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.402 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.403 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.404 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.405 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.406 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.408 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.409 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.411 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.412 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.413 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.415 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.416 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.417 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.418 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.419 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.421 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${flowable-feign-server-name:eam-flowable}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-06-12 11:14:08.670 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'xssCleaner' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=initBeans; factoryMethodName=xssCleaner; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/init/InitBeans.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=net.dreamlu.mica.xss.config.MicaXssConfiguration; factoryMethodName=xssCleaner; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [net/dreamlu/mica/xss/config/MicaXssConfiguration.class]]
2025-06-12 11:14:09.284 INFO [restartedMain] org.springframework.data.repository.config.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-12 11:14:09.287 INFO [restartedMain] org.springframework.data.repository.config.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-12 11:14:09.647 INFO [restartedMain] org.springframework.data.repository.config.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 333 ms. Found 0 Redis repository interfaces.
2025-06-12 11:14:09.741 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'org.springframework.transaction.config.internalTransactionAdvisor' with a different definition: replacing [Root bean: class=org.springframework.transaction.interceptor.BeanFactoryTransactionAttributeSourceAdvisor; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration; factoryMethodName=transactionAdvisor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [org/springframework/transaction/annotation/ProxyTransactionManagementConfiguration.class]]
2025-06-12 11:14:10.063 INFO [restartedMain] com.uino.api.init.LocalRunConfig : 发现spring-boot配置为本地加载
2025-06-12 11:14:10.114 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : 开始注册Filter相关信息
2025-06-12 11:14:10.190 INFO [restartedMain] org.springframework.cloud.context.scope.GenericScope : BeanFactory id=9f66b836-beed-3a7c-baa6-397678cf3cdf
2025-06-12 11:14:11.568 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.uinnova.product.eam.config.RedisConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-12 11:14:11.592 INFO [restartedMain] com.uinnova.product.eam.config.RedisConfig : 初始化 -> [Redis CacheErrorHandler]
2025-06-12 11:14:11.594 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'errorHandler' of type [com.uinnova.product.eam.config.RedisConfig$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-12 11:14:13.058 INFO [restartedMain] org.springframework.boot.web.embedded.tomcat.TomcatWebServer : Tomcat initialized with port 1515 (http)
2025-06-12 11:14:13.080 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-1515"]
2025-06-12 11:14:13.082 INFO [restartedMain] org.apache.catalina.core.StandardService : Starting service [Tomcat]
2025-06-12 11:14:13.083 INFO [restartedMain] org.apache.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/11.0.6]
2025-06-12 11:14:13.269 INFO [restartedMain] org.apache.jasper.servlet.TldScanner : At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-06-12 11:14:13.278 INFO [restartedMain] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring embedded WebApplicationContext
2025-06-12 11:14:13.280 INFO [restartedMain] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 8005 ms
2025-06-12 11:14:13.298 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : ResponseFilter注册成功
2025-06-12 11:14:13.299 INFO [restartedMain] com.uino.init.http.ResponseFilter : response请求拦截器注册成功
2025-06-12 11:14:13.327 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : RefererFilter注册成功
2025-06-12 11:14:13.330 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : StaticFilter注册成功
2025-06-12 11:14:13.331 INFO [restartedMain] com.uino.init.http.StaticFilter : 静态资源请求拦截器注册成功
2025-06-12 11:14:13.335 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PermissionFilter注册成功
2025-06-12 11:14:13.340 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PluginOperateLogFilter注册成功
2025-06-12 11:14:23.711 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_visualmodel
2025-06-12 11:14:23.977 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_artifact
2025-06-12 11:14:24.223 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：monet_diagram_node
2025-06-12 11:14:24.786 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_attr_trans_config
2025-06-12 11:14:25.143 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_ci_history
2025-06-12 11:14:25.465 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_ci_operate_log
2025-06-12 11:14:25.630 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_dictionary_class
2025-06-12 11:14:25.938 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_dictionary_item
2025-06-12 11:14:26.575 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_ciclass
2025-06-12 11:14:26.841 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：monet_diagram_link
2025-06-12 11:14:27.078 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_rltclass
2025-06-12 11:14:27.452 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_rlt
2025-06-12 11:14:27.606 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_ciclassrlt
2025-06-12 11:14:27.825 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_monet_diagram
2025-06-12 11:14:29.932 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_monet_diagram_sheet
2025-06-12 11:14:30.153 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_share_diagram
2025-06-12 11:14:30.307 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_share_link
2025-06-12 11:14:30.490 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_resource
2025-06-12 11:14:30.506 WARN [restartedMain] com.amazonaws.util.VersionInfoUtils : The AWS SDK for Java 1.x entered maintenance mode starting July 31, 2024 and will reach end of support on December 31, 2025. For more information, see https://aws.amazon.com/blogs/developer/the-aws-sdk-for-java-1-x-is-in-maintenance-mode-effective-july-31-2024/\nYou can print where on the file system the AWS SDK for Java 1.x core runtime is located by setting the AWS_JAVA_V1_PRINT_LOCATION environment variable or aws.java.v1.printLocation system property to 'true'.\nThis message can be disabled by setting the AWS_JAVA_V1_DISABLE_DEPRECATION_ANNOUNCEMENT environment variable or aws.java.v1.disableDeprecationAnnouncement system property to 'true'.\nThe AWS SDK for Java 1.x is being used here:\nat java.base/java.lang.Thread.getStackTrace(Thread.java:1610)\nat com.amazonaws.util.VersionInfoUtils.printDeprecationAnnouncement(VersionInfoUtils.java:81)\nat com.amazonaws.util.VersionInfoUtils.<clinit>(VersionInfoUtils.java:59)\nat com.amazonaws.ClientConfiguration.<clinit>(ClientConfiguration.java:95)\nat com.uino.util.rsm.AliyunObsRsmBehavior.initClient(AliyunObsRsmBehavior.java:48)\nat com.uino.util.rsm.AliyunObsRsmBehavior.<init>(AliyunObsRsmBehavior.java:35)\nat com.uino.util.rsm.RsmUtils.initRsmBehavior(RsmUtils.java:65)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1810)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)\nat org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)\nat org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)\nat org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)\nat org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)\nat org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:318)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)\nat com.uinnova.product.eam.EamApplication.main(EamApplication.java:39)\nat com.uinnova.product.eam.web.test.RunLocal.main(RunLocal.java:10)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
2025-06-12 11:14:30.925 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_artifact_element
2025-06-12 11:14:31.096 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_resource
2025-06-12 11:14:31.376 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_hierarchy
2025-06-12 11:14:31.555 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_category_private
2025-06-12 11:14:31.681 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_category_design
2025-06-12 11:14:31.818 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_multi_model_type
2025-06-12 11:14:32.027 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_multi_model_hierarchy
2025-06-12 11:14:32.397 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_asset_warehouse_dir
2025-06-12 11:14:32.575 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_version_dir
2025-06-12 11:14:32.714 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_version_tag
2025-06-12 11:14:32.936 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_plan_design_instance
2025-06-12 11:14:33.098 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_plan_chapter_instance
2025-06-12 11:14:33.226 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_plan_template_chapter
2025-06-12 11:14:33.368 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_chapter_resource
2025-06-12 11:14:33.534 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_plan_template_chapter_data
2025-06-12 11:14:33.736 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_plan_chapter_context
2025-06-12 11:14:34.069 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_plan_module_annotation
2025-06-12 11:14:34.218 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_plan_chapter_question
2025-06-12 11:14:34.394 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : RedissonClient, type : single
2025-06-12 11:14:34.404 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : Create redissonClient.
2025-06-12 11:14:34.503 INFO [restartedMain] org.redisson.Version : Redisson 3.22.0
2025-06-12 11:14:34.896 INFO [redisson-netty-2-5] org.redisson.connection.pool.MasterPubSubConnectionPool : 1 connections initialized for 192.168.21.144/192.168.21.144:6379
2025-06-12 11:14:34.982 INFO [redisson-netty-2-19] org.redisson.connection.pool.MasterConnectionPool : 24 connections initialized for 192.168.21.144/192.168.21.144:6379
2025-06-12 11:14:35.306 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_oauth_client
2025-06-12 11:14:35.531 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_oauth_resource
2025-06-12 11:14:35.831 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_oauth_token
2025-06-12 11:14:35.976 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_oauth_refreshtoken
2025-06-12 11:14:36.156 INFO [restartedMain] org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer : LiveReload server is running on port 35729
2025-06-12 11:14:36.193 INFO [restartedMain] org.springframework.cloud.openfeign.FeignClientFactoryBean : For 'eam-flowable' URL not provided. Will try picking an instance via load-balancing.
2025-06-12 11:14:36.552 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_plan_chapter_collaborate
2025-06-12 11:14:37.253 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_plan_design_share_record
2025-06-12 11:14:37.413 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_class_setting
2025-06-12 11:14:37.576 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_dir_setting
2025-06-12 11:14:37.706 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_deliverable_template
2025-06-12 11:14:37.857 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_template_type
2025-06-12 11:14:38.031 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_attention
2025-06-12 11:14:38.284 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_plan_artifact
2025-06-12 11:14:38.485 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_tag
2025-06-12 11:14:38.949 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_ci_history_design
2025-06-12 11:14:39.100 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_no_compliance_design_ci
2025-06-12 11:14:39.536 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_ci_design
2025-06-12 11:14:39.797 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_no_compliance_design_rlt
2025-06-12 11:14:40.187 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_rlt_design
2025-06-12 11:14:40.384 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_flow_system_associated_features
2025-06-12 11:14:40.650 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_workbench_charge_done
2025-06-12 11:14:40.864 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_asset_list_attr_conf
2025-06-12 11:14:41.054 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_asset_detail_attr_conf
2025-06-12 11:14:41.261 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_user_org_rlt
2025-06-12 11:14:41.439 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_user_role_rlt
2025-06-12 11:14:41.653 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_org_role_rlt
2025-06-12 11:14:41.837 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_role_module_rlt
2025-06-12 11:14:42.079 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_role_datamodule_rlt
2025-06-12 11:14:42.250 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_role
2025-06-12 11:14:42.467 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_user_module_rlt
2025-06-12 11:14:42.652 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_user_datamodule_rlt
2025-06-12 11:14:42.787 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_user_module_enshrine_rlt
2025-06-12 11:14:42.932 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_type_config
2025-06-12 11:14:43.140 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_module
2025-06-12 11:14:43.440 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_no_compliance_private_ci
2025-06-12 11:14:43.742 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_ci_private
2025-06-12 11:14:43.967 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_square_config
2025-06-12 11:14:44.280 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_dataset
2025-06-12 11:14:44.445 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_dataset_coordination
2025-06-12 11:14:44.584 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_dataset_top
2025-06-12 11:14:44.753 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_dataset_priority_display
2025-06-12 11:14:44.895 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_dataset_mallapi_log
2025-06-12 11:14:45.046 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_dataset_exe_result
2025-06-12 11:14:45.177 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_dataset_exe_result_sheet
2025-06-12 11:14:45.688 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_dataset_task_lock
2025-06-12 11:14:45.840 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_digital_twin_service_portal
2025-06-12 11:14:46.060 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_tp_rule
2025-06-12 11:14:46.258 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_directory_object_association_decision
2025-06-12 11:14:46.434 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_image
2025-06-12 11:14:47.073 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_ci_rlt_history_design
2025-06-12 11:14:47.514 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_diagram_relation_module
2025-06-12 11:14:47.675 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_plan_module_delete
2025-06-12 11:14:47.770 INFO [restartedMain] com.uinnova.product.eam.init.EamLocalRunConfig$$SpringCGLIB$$0 : 发现spring-boot配置为本地加载
2025-06-12 11:14:47.924 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_diagram_relation_sys
2025-06-12 11:14:48.068 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_diagram_relation_sys_private
2025-06-12 11:14:48.298 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_folder_permission_manager
2025-06-12 11:14:48.466 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_no_compliance_private_rlt
2025-06-12 11:14:48.840 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_rlt_private
2025-06-12 11:14:49.173 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_matrix_instance_private
2025-06-12 11:14:49.322 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_matrix_instance_design
2025-06-12 11:14:49.475 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_matrix_data_private
2025-06-12 11:14:49.665 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_matrix_data_design
2025-06-12 11:14:49.923 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_flow_system_approve_data
2025-06-12 11:14:50.172 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_notice
2025-06-12 11:14:50.363 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_diagram_approve_rlt
2025-06-12 11:14:50.549 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_matrix_stencil
2025-06-12 11:14:51.817 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_app_sync_record
2025-06-12 11:14:52.006 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：ci_rlt_del_param
2025-06-12 11:14:52.226 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_ci_rlt_del_info
2025-06-12 11:14:52.415 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_flow_system_publish_history
2025-06-12 11:14:52.554 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_flow_system_associated_features_private
2025-06-12 11:14:52.690 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_flow_system_file
2025-06-12 11:14:52.859 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_flow_system_file_private
2025-06-12 11:14:53.035 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_flow_system_process_sign_data
2025-06-12 11:14:53.199 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_indicator_detection_information_association
2025-06-12 11:14:53.618 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_recently_view
2025-06-12 11:14:53.781 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_diagram_auto_layout_conf
2025-06-12 11:14:53.959 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_hierarchy_navigation
2025-06-12 11:14:54.180 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_dir
2025-06-12 11:14:54.546 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_asset_data_screen
2025-06-12 11:14:54.868 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_flow_system_status
2025-06-12 11:14:55.085 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_flow_system_operation_data
2025-06-12 11:14:55.337 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_resource
2025-06-12 11:14:55.484 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_plan_system_relation
2025-06-12 11:14:55.649 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_dir_relation_plan
2025-06-12 11:14:55.825 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_dataset_snapshot
2025-06-12 11:14:56.009 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_ci_table_snapshot
2025-06-12 11:14:56.163 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_tem_dia_relation
2025-06-12 11:14:56.486 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_base_config
2025-06-12 11:14:56.677 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_ciclass_encode
2025-06-12 11:14:56.866 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_asset_panorama_detail_conf
2025-06-12 11:14:57.030 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_asset_panorama_info
2025-06-12 11:14:57.223 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_configuration
2025-06-12 11:14:57.382 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_asset_change_record
2025-06-12 11:14:57.644 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_arch_decision
2025-06-12 11:14:57.969 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_screen_config
2025-06-12 11:14:58.453 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_ci_history_private
2025-06-12 11:14:59.254 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_ci_rlt_history_private
2025-06-12 11:14:59.597 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_visualmodel_history
2025-06-12 11:14:59.765 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_visualmodel_rlt_history
2025-06-12 11:14:59.960 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_visualmodel_class_history
2025-06-12 11:15:00.120 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_model_panorama_3d
2025-06-12 11:15:00.308 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_visualmodel_private
2025-06-12 11:15:00.657 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cj_template_bind
2025-06-12 11:15:01.427 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_boot_entry_config
2025-06-12 11:15:01.752 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_boot_function_config
2025-06-12 11:15:01.922 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_boot_page_progress
2025-06-12 11:15:02.091 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_boot_trial_page_progress
2025-06-12 11:15:02.309 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_boot_user_config
2025-06-12 11:15:02.673 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_dircollaboratorinfo
2025-06-12 11:15:02.951 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_org
2025-06-12 11:15:03.176 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_applicationsysfigurerlt
2025-06-12 11:15:03.667 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_feedback
2025-06-12 11:15:03.878 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_listing
2025-06-12 11:15:04.080 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_listing_type
2025-06-12 11:15:04.308 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_resolution
2025-06-12 11:15:04.499 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_resolution_doc
2025-06-12 11:15:04.749 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_standard
2025-06-12 11:15:04.922 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_standard_doc
2025-06-12 11:15:05.119 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_word_doc
2025-06-12 11:15:05.406 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_diagram_catalog_private
2025-06-12 11:15:05.572 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_diagram_catalog_design
2025-06-12 11:15:05.741 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_diagram_dir
2025-06-12 11:15:06.027 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_folder_approval_manager
2025-06-12 11:15:06.297 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_interface_parameters
2025-06-12 11:15:06.499 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_new_user
2025-06-12 11:15:06.683 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_user_enterprise
2025-06-12 11:15:06.916 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_upload_manage
2025-06-12 11:15:07.199 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_license_auth
2025-06-12 11:15:07.414 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_license_auth_server
2025-06-12 11:15:07.690 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_workbench_layout_conf
2025-06-12 11:15:07.933 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_flow_activity_operation_data
2025-06-12 11:15:08.266 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_my_shape_dir
2025-06-12 11:15:08.439 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_my_shape
2025-06-12 11:15:08.716 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_template_dir
2025-06-12 11:15:09.083 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_ci_tree_config
2025-06-12 11:15:09.455 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_cirlt_auto_build
2025-06-12 11:15:09.777 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_top
2025-06-12 11:15:10.129 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_monitor_simulation_record_event
2025-06-12 11:15:10.214 ERROR[restartedMain] com.uino.dao.AbstractSplitBaseDao : init index mapping config error :: index== [mon_eap_event_all_202506] :: type== [mon_eap_event_all_202506]  Elasticsearch exception [type=mapper_parsing_exception, reason=Failed to parse mapping [_doc]: Root mapping definition has unsupported parameters:  [mon_eap_event_all_202506 : {properties={time={type=date}, value={type=double}}}]]
org.elasticsearch.ElasticsearchStatusException: Elasticsearch exception [type=mapper_parsing_exception, reason=Failed to parse mapping [_doc]: Root mapping definition has unsupported parameters:  [mon_eap_event_all_202506 : {properties={time={type=date}, value={type=double}}}]]
	at org.elasticsearch.rest.BytesRestResponse.errorFromXContent(BytesRestResponse.java:178) ~[elasticsearch-7.17.28.jar:7.17.28]
	at org.elasticsearch.client.RestHighLevelClient.parseEntity(RestHighLevelClient.java:2484) ~[elasticsearch-rest-high-level-client-7.17.28.jar:8.15.5]
	at org.elasticsearch.client.RestHighLevelClient.parseResponseException(RestHighLevelClient.java:2461) ~[elasticsearch-rest-high-level-client-7.17.28.jar:8.15.5]
	at org.elasticsearch.client.RestHighLevelClient.internalPerformRequest(RestHighLevelClient.java:2184) ~[elasticsearch-rest-high-level-client-7.17.28.jar:8.15.5]
	at org.elasticsearch.client.RestHighLevelClient.performRequest(RestHighLevelClient.java:2154) ~[elasticsearch-rest-high-level-client-7.17.28.jar:8.15.5]
	at org.elasticsearch.client.RestHighLevelClient.performRequestAndParseEntity(RestHighLevelClient.java:2118) ~[elasticsearch-rest-high-level-client-7.17.28.jar:8.15.5]
	at org.elasticsearch.client.IndicesClient.create(IndicesClient.java:152) ~[elasticsearch-rest-high-level-client-7.17.28.jar:8.15.5]
	at com.uino.dao.AbstractSplitBaseDao.initIndex(AbstractSplitBaseDao.java:244) ~[uino-eam-micro-dao-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.uino.dao.AbstractSplitBaseDao.initIndexByName(AbstractSplitBaseDao.java:212) ~[uino-eam-micro-dao-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.uino.dao.AbstractSplitBaseDao.cacheIndex(AbstractSplitBaseDao.java:160) ~[uino-eam-micro-dao-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[?:?]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1810) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371) [spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) [spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) [spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221) [spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187) [spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122) [spring-beans-6.2.6.jar:6.2.6]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987) [spring-context-6.2.6.jar:6.2.6]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) [spring-context-6.2.6.jar:6.2.6]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) [spring-boot-3.4.5.jar:3.4.5]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753) [spring-boot-3.4.5.jar:3.4.5]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) [spring-boot-3.4.5.jar:3.4.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-3.4.5.jar:3.4.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362) [spring-boot-3.4.5.jar:3.4.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351) [spring-boot-3.4.5.jar:3.4.5]
	at com.uinnova.product.eam.EamApplication.main(EamApplication.java:39) [classes/:?]
	at com.uinnova.product.eam.web.test.RunLocal.main(RunLocal.java:10) [test-classes/:?]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[?:?]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-3.4.5.jar:3.4.5]
	Suppressed: org.elasticsearch.client.ResponseException: method [PUT], host [http://*************:29200], URI [/mon_eap_event_all_202506?master_timeout=30s&timeout=30s], status line [HTTP/1.1 400 Bad Request]
{"error":{"root_cause":[{"type":"mapper_parsing_exception","reason":"Root mapping definition has unsupported parameters:  [mon_eap_event_all_202506 : {properties={time={type=date}, value={type=double}}}]"}],"type":"mapper_parsing_exception","reason":"Failed to parse mapping [_doc]: Root mapping definition has unsupported parameters:  [mon_eap_event_all_202506 : {properties={time={type=date}, value={type=double}}}]","caused_by":{"type":"mapper_parsing_exception","reason":"Root mapping definition has unsupported parameters:  [mon_eap_event_all_202506 : {properties={time={type=date}, value={type=double}}}]"}},"status":400}
		at org.elasticsearch.client.RestClient.convertResponse(RestClient.java:351) ~[elasticsearch-rest-client-8.15.5.jar:8.15.5]
		at org.elasticsearch.client.RestClient.performRequest(RestClient.java:317) ~[elasticsearch-rest-client-8.15.5.jar:8.15.5]
		at org.elasticsearch.client.RestClient.performRequest(RestClient.java:292) ~[elasticsearch-rest-client-8.15.5.jar:8.15.5]
		at org.elasticsearch.client.RestHighLevelClient.performClientRequest(RestHighLevelClient.java:2699) ~[elasticsearch-rest-high-level-client-7.17.28.jar:8.15.5]
		at org.elasticsearch.client.RestHighLevelClient.internalPerformRequest(RestHighLevelClient.java:2171) ~[elasticsearch-rest-high-level-client-7.17.28.jar:8.15.5]
		at org.elasticsearch.client.RestHighLevelClient.performRequest(RestHighLevelClient.java:2154) ~[elasticsearch-rest-high-level-client-7.17.28.jar:8.15.5]
		at org.elasticsearch.client.RestHighLevelClient.performRequestAndParseEntity(RestHighLevelClient.java:2118) ~[elasticsearch-rest-high-level-client-7.17.28.jar:8.15.5]
		at org.elasticsearch.client.IndicesClient.create(IndicesClient.java:152) ~[elasticsearch-rest-high-level-client-7.17.28.jar:8.15.5]
		at com.uino.dao.AbstractSplitBaseDao.initIndex(AbstractSplitBaseDao.java:244) ~[uino-eam-micro-dao-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
		at com.uino.dao.AbstractSplitBaseDao.initIndexByName(AbstractSplitBaseDao.java:212) ~[uino-eam-micro-dao-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
		at com.uino.dao.AbstractSplitBaseDao.cacheIndex(AbstractSplitBaseDao.java:160) ~[uino-eam-micro-dao-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
		at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[?:?]
		at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1810) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371) [spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) [spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) [spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221) [spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187) [spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122) [spring-beans-6.2.6.jar:6.2.6]
		at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987) [spring-context-6.2.6.jar:6.2.6]
		at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) [spring-context-6.2.6.jar:6.2.6]
		at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) [spring-boot-3.4.5.jar:3.4.5]
		at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753) [spring-boot-3.4.5.jar:3.4.5]
		at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) [spring-boot-3.4.5.jar:3.4.5]
		at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-3.4.5.jar:3.4.5]
		at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362) [spring-boot-3.4.5.jar:3.4.5]
		at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351) [spring-boot-3.4.5.jar:3.4.5]
		at com.uinnova.product.eam.EamApplication.main(EamApplication.java:39) [classes/:?]
		at com.uinnova.product.eam.web.test.RunLocal.main(RunLocal.java:10) [test-classes/:?]
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
		at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[?:?]
		at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) [spring-boot-devtools-3.4.5.jar:3.4.5]
Caused by: org.elasticsearch.ElasticsearchException: Elasticsearch exception [type=mapper_parsing_exception, reason=Root mapping definition has unsupported parameters:  [mon_eap_event_all_202506 : {properties={time={type=date}, value={type=double}}}]]
	at org.elasticsearch.ElasticsearchException.innerFromXContent(ElasticsearchException.java:520) ~[elasticsearch-7.17.28.jar:7.17.28]
	at org.elasticsearch.ElasticsearchException.fromXContent(ElasticsearchException.java:431) ~[elasticsearch-7.17.28.jar:7.17.28]
	at org.elasticsearch.ElasticsearchException.innerFromXContent(ElasticsearchException.java:461) ~[elasticsearch-7.17.28.jar:7.17.28]
	at org.elasticsearch.ElasticsearchException.failureFromXContent(ElasticsearchException.java:627) ~[elasticsearch-7.17.28.jar:7.17.28]
	at org.elasticsearch.rest.BytesRestResponse.errorFromXContent(BytesRestResponse.java:170) ~[elasticsearch-7.17.28.jar:7.17.28]
	... 68 more
2025-06-12 11:15:10.453 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：event
2025-06-12 11:15:10.956 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_simulation_rule
2025-06-12 11:15:11.210 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_plugin
2025-06-12 11:15:11.414 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_notify_channel
2025-06-12 11:15:11.628 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_login_log
2025-06-12 11:15:11.845 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_login_auth_integration
2025-06-12 11:15:12.227 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_operate_log
2025-06-12 11:15:12.645 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_operate_log_module
2025-06-12 11:15:12.834 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_table_data_config
2025-06-12 11:15:13.069 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_tenant_domain
2025-06-12 11:15:13.073 INFO [restartedMain] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor : Autowired annotation is not supported on static fields: private static java.lang.String com.uino.service.sys.microservice.impl.TenantDomainDataOperate.httpPath
2025-06-12 11:15:13.116 INFO [restartedMain] com.uino.init.ControllerAspect : web请求日志切面注册成功
2025-06-12 11:15:13.147 INFO [restartedMain] com.uino.init.ExceptionController : 异常处理类注册成功
2025-06-12 11:15:13.155 INFO [restartedMain] com.uino.init.InitBeans : 开始注册i18-client，修改i18n.files可修改对应国际化文件读取，支持逗号隔开字符串
2025-06-12 11:15:13.165 INFO [restartedMain] com.uino.init.InitBeans : 注册i18-client成功
2025-06-12 11:15:13.166 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  has refresh language data ... 
2025-06-12 11:15:13.166 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  load refresh language data [114]. 
2025-06-12 11:15:13.212 INFO [restartedMain] com.uino.init.JudgeProcessBeans : 注册校验链
2025-06-12 11:15:13.404 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_url_patch_manager
2025-06-12 11:15:13.839 INFO [restartedMain] com.uino.oauth.common.service.UinoLdapAuthenticationProvider : init  ldap user details manager !
2025-06-12 11:15:14.022 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_session_cache
2025-06-12 11:15:14.075 INFO [restartedMain] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor : Autowired annotation is not supported on static fields: private static java.lang.String com.uinnova.product.eam.base.diagram.utils.CommonUtil.prefix1
2025-06-12 11:15:14.075 INFO [restartedMain] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor : Autowired annotation is not supported on static fields: private static java.lang.String com.uinnova.product.eam.base.diagram.utils.CommonUtil.prefix2
2025-06-12 11:15:14.301 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_logo
2025-06-12 11:15:14.749 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_ci
2025-06-12 11:15:15.103 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_ci_rlt_history
2025-06-12 11:15:15.277 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_impactpath
2025-06-12 11:15:15.449 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_cmdb_rlt_rule
2025-06-12 11:15:15.454 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : init SpecifyIndexDao
2025-06-12 11:15:15.454 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : *************:29200
2025-06-12 11:15:15.663 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_tp_metric_label
2025-06-12 11:15:15.941 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_op_counter
2025-06-12 11:15:16.082 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_system_severity
2025-06-12 11:15:16.289 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_subsystem_approve_edit
2025-06-12 11:15:16.450 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_subsystem_temporary
2025-06-12 11:15:16.589 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_eam_diagram_dir_temp
2025-06-12 11:15:16.827 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：cj_sys_dir_relation
2025-06-12 11:15:17.646 INFO [restartedMain] org.springframework.validation.beanvalidation.OptionalValidatorFactoryBean : Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
2025-06-12 11:15:17.783 INFO [restartedMain] com.uino.init.InitBeans : 国际化默认语言为【ZHC】，可通过修改i18n.lang.default修改默认语言，属性范围参考class:Language
2025-06-12 11:15:17.927 INFO [restartedMain] com.uino.init.InitFrameProp : FrameworkProperties注册成功, Local Space http://192.168.21.144/rsm
2025-06-12 11:15:18.153 INFO [restartedMain] org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name uinoUserDetailsManager
2025-06-12 11:15:18.273 WARN [restartedMain] org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer$AuthorizationManagerRequestMatcherRegistry : One of the patterns in [/system-setting#/license, ,/system/init/initFlowData, /eam/workbenchChargeDone/openApi/todoCount, /sys/getLogos, /eam/workbenchChargeDone/changeAction, /eam/workbenchChargeDone/saveOrUpdate, /sync/syncUserDataBatchToEa, /permission/oauth/resource/cleanOnlineUser, /wiki/getTokenByCode, /login/getLoginMethod, /cj/system/diagram/changeFlowByDiagramIds, /planDesign/updatePlanDiagramIsFlow, /redirectAuth, /getTokenByCode, /refreshToken, /cmdb/dataSet/execute, /eam/user/getUserByRoleName, /cmdb/dataSet/realTimeExecute, /websocket/*/*, /eam/oauth/getTokenByLoginInfo, /trial/saas/login/check, /eam/notice/workflow/msg/save, /planDesign/getPlanForFeign, /planDesign/findRenewVersionPlanList, /flowable/getApprovalUser, /flowable/approval/task, /flowable/batchModifyWorkbenchTask, /rsm/**, /ai/api/**, /webjars/css/*.css, /webjars/js/*.js, /swagger-resources, /v2/api-docs, /doc.html] is missing a leading slash. This is discouraged; please include the leading slash in all your request matcher patterns. In future versions of Spring Security, leaving out the leading slash will result in an exception.
2025-06-12 11:15:19.146 INFO [restartedMain] org.springframework.ldap.core.support.AbstractContextSource : Property 'userDn' not set - anonymous context will be used for read-only operations
2025-06-12 11:15:19.660 INFO [restartedMain] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver : Exposing 1 endpoint beneath base path '/actuator'
2025-06-12 11:15:21.341 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Using default implementation for ThreadExecutor
2025-06-12 11:15:21.354 INFO [restartedMain] org.quartz.core.SchedulerSignalerImpl : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-12 11:15:21.354 INFO [restartedMain] org.quartz.core.QuartzScheduler : Quartz Scheduler v.2.3.2 created.
2025-06-12 11:15:21.354 INFO [restartedMain] org.quartz.simpl.RAMJobStore : RAMJobStore initialized.
2025-06-12 11:15:21.355 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'\n  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.\n  NOT STARTED.\n  Currently in standby mode.\n  Number of jobs executed: 0\n  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.\n  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.\n
2025-06-12 11:15:21.355 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-12 11:15:21.355 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler version: 2.3.2
2025-06-12 11:15:21.355 INFO [restartedMain] org.quartz.core.QuartzScheduler : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2490bc55
2025-06-12 11:15:22.474 INFO [restartedMain] org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler : Starting...
2025-06-12 11:15:22.475 INFO [restartedMain] org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@317beac5]]
2025-06-12 11:15:22.478 INFO [restartedMain] org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler : Started.
2025-06-12 11:15:22.478 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-1515"]
2025-06-12 11:15:22.497 INFO [restartedMain] org.springframework.boot.web.embedded.tomcat.TomcatWebServer : Tomcat started on port 1515 (http) with context path '/tarsier-eam'
2025-06-12 11:15:22.563 INFO [restartedMain] com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-12 11:15:22.564 INFO [restartedMain] com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-12 11:15:23.572 INFO [restartedMain] com.alibaba.nacos.common.ability.AbstractAbilityControlManager : Ready to get current node abilities...
2025-06-12 11:15:23.573 INFO [restartedMain] com.alibaba.nacos.common.ability.AbstractAbilityControlManager : Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-06-12 11:15:23.575 INFO [restartedMain] com.alibaba.nacos.common.ability.AbstractAbilityControlManager : Initialize current abilities finish...
2025-06-12 11:15:23.575 INFO [restartedMain] com.alibaba.nacos.common.ability.discover.NacosAbilityManagerHolder : [AbilityControlManager] Successfully initialize AbilityControlManager
2025-06-12 11:15:23.710 INFO [restartedMain] com.alibaba.cloud.nacos.registry.NacosServiceRegistry : nacos registry, DEFAULT_GROUP eam-fuxi 192.168.205.1:1515 register finished
2025-06-12 11:15:23.852 INFO [restartedMain] org.springframework.scheduling.quartz.SchedulerFactoryBean : Starting Quartz Scheduler now
2025-06-12 11:15:23.852 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-12 11:15:23.900 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-12 11:15:23.902 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-12 11:15:25.071 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_data_module
2025-06-12 11:15:25.348 INFO [restartedMain] com.uino.dao.AbstractESBaseDao : 初始化索引成功：uino_sys_user
2025-06-12 11:15:25.415 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-12 11:15:25.598 WARN [restartedMain] org.elasticsearch.client.RestClient : request [PUT http://*************:29200/performance_202506?master_timeout=30s&timeout=30s] returned 2 warnings: [299 Elasticsearch-7.10.1-1c34507e66d7db1211f66f3513706fdf548736aa "dynamic template [string_code_stdkeyword] has invalid content [{\"match\":\".*?name|.*?code|.*?key|.*?Name|.*?Code|.*?Key\",\"match_mapping_type\":\"string\",\"match_pattern\":\"regex\",\"mapping\":{\"analyzer\":\"my-analyzer\",\"fields\":{\"keyword\":{\"ignore_above\":256,\"type\":\"keyword\"},\"stdkeyword\":{\"normalizer\":\"my_normalizer\",\"ignore_above\":256,\"type\":\"keyword\"}},\"type\":\"text\"}}], caused by [analyzer [my-analyzer] has not been configured in mappings]"],[299 Elasticsearch-7.10.1-1c34507e66d7db1211f66f3513706fdf548736aa "dynamic template [strings] has invalid content [{\"match_mapping_type\":\"string\",\"mapping\":{\"analyzer\":\"my-analyzer\",\"fields\":{\"keyword\":{\"ignore_above\":256,\"type\":\"keyword\"}},\"type\":\"text\"}}], caused by [analyzer [my-analyzer] has not been configured in mappings]"]
2025-06-12 11:15:25.649 WARN [restartedMain] org.elasticsearch.client.RestClient : request [PUT http://*************:29200/performance_202506/_settings?master_timeout=30s&timeout=30s] returned 2 warnings: [299 Elasticsearch-7.10.1-1c34507e66d7db1211f66f3513706fdf548736aa "dynamic template [string_code_stdkeyword] has invalid content [{\"match\":\".*?name|.*?code|.*?key|.*?Name|.*?Code|.*?Key\",\"match_mapping_type\":\"string\",\"match_pattern\":\"regex\",\"mapping\":{\"analyzer\":\"my-analyzer\",\"fields\":{\"keyword\":{\"ignore_above\":256,\"type\":\"keyword\"},\"stdkeyword\":{\"normalizer\":\"my_normalizer\",\"ignore_above\":256,\"type\":\"keyword\"}},\"type\":\"text\"}}], caused by [analyzer [my-analyzer] has not been configured in mappings]"],[299 Elasticsearch-7.10.1-1c34507e66d7db1211f66f3513706fdf548736aa "dynamic template [strings] has invalid content [{\"match_mapping_type\":\"string\",\"mapping\":{\"analyzer\":\"my-analyzer\",\"fields\":{\"keyword\":{\"ignore_above\":256,\"type\":\"keyword\"}},\"type\":\"text\"}}], caused by [analyzer [my-analyzer] has not been configured in mappings]"]
2025-06-12 11:15:25.755 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Started EamApplication in 81.637 seconds (process running for 83.541)
2025-06-12 11:15:25.770 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-12 11:15:25.773 INFO [restartedMain] com.uinnova.product.eam.service.utils.ConvertBusinessKeyUtil : 转换businessKey: false
2025-06-12 11:15:25.788 INFO [restartedMain] com.uinnova.product.eam.init.FlowSystemDataInit : 流程体系数据初始化开关关闭
2025-06-12 11:15:25.875 INFO [restartedMain] com.uinnova.product.eam.web.mix.diagram.v2.webSocekt.DiagramServer : DiagramServer start success
2025-06-12 11:15:25.875 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-12 11:15:25.993 INFO [restartedMain] com.uinnova.product.eam.EamApplication : EAM application start success :)
2025-06-12 11:16:22.392 INFO [MessageBroker-2] org.springframework.web.socket.config.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 10, active threads = 1, queued tasks = 4, completed tasks = 5]
