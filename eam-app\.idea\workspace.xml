<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="05eadd8a-a335-46be-8a97-60b8382faf1a" name="更改" comment="fix：关系绘图接口兼容未发布的ci，未发布直接返回空集合&#10;tapd：【【画布】关系绘图】&#10;     https://www.tapd.cn/tapd_fe/33608965/story/detail/1133608965001131388">
      <change beforePath="$PROJECT_DIR$/eam-web/src/test/resources/application-local.properties" beforeDir="false" afterPath="$PROJECT_DIR$/eam-web/src/test/resources/application-local.properties" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="develop-v6.5.0-lichong" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="MIXED" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\Green Program Files\apache-maven-3.8.4" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2xcFlnUMs59qS7xnFFHUv31Pl4Y" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.eam [clean].executor": "Run",
    "Maven.eam [compile].executor": "Run",
    "Maven.eam [install].executor": "Run",
    "Maven.eam [package].executor": "Run",
    "Maven.eam-service [package].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHELLCHECK.PATH": "我会在意",
    "git-widget-placeholder": "develop-v6.5.0-jdk17-spring3.4.5",
    "go.import.settings.migrated": "true",
    "last_opened_file_path": "D:/workspace/mycode/ea/eam-app",
    "project.structure.last.edited": "项目",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.30229884",
    "settings.editor.selected.configurable": "com.codeium.AppSettingsConfigurable",
    "应用程序.RunLocal.executor": "Debug"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager" selected="应用程序.RunLocal">
    <configuration name="RunLocal" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.uinnova.product.eam.web.test.RunLocal" />
      <module name="eam-web" />
      <shortenClasspath name="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EamWorkableApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="eam-workable" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.uinnova.product.eam.workable.EamWorkableApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.EamWorkableApplication" />
      <item itemvalue="应用程序.RunLocal" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.RunLocal" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="05eadd8a-a335-46be-8a97-60b8382faf1a" name="更改" comment="" />
      <created>1748229609139</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748229609139</updated>
      <workItem from="1748229611021" duration="85000" />
      <workItem from="1748229734454" duration="2863000" />
      <workItem from="1748238004313" duration="10531000" />
      <workItem from="1748254956049" duration="34000" />
      <workItem from="1748254991400" duration="11551000" />
      <workItem from="1748325391973" duration="1080000" />
      <workItem from="1748326486734" duration="146000" />
      <workItem from="1748415935435" duration="5900000" />
      <workItem from="1748421915189" duration="4069000" />
      <workItem from="1748427454146" duration="54538000" />
      <workItem from="1748914076447" duration="2224000" />
      <workItem from="1748916502335" duration="153000" />
      <workItem from="1748916677512" duration="176000" />
      <workItem from="1748916882778" duration="10590000" />
      <workItem from="1748938694430" duration="96000" />
      <workItem from="1748938803682" duration="268000" />
      <workItem from="1748939101182" duration="2018000" />
      <workItem from="1748941804818" duration="3595000" />
      <workItem from="1748945840375" duration="18660000" />
      <workItem from="1749024408532" duration="1605000" />
      <workItem from="1749027449005" duration="115000" />
      <workItem from="1749027587957" duration="207000" />
      <workItem from="1749027818623" duration="12837000" />
      <workItem from="1749109046699" duration="300000" />
      <workItem from="1749109359048" duration="18187000" />
      <workItem from="1749192455966" duration="12579000" />
      <workItem from="1749433271335" duration="36672000" />
      <workItem from="1749524964068" duration="8664000" />
      <workItem from="1749538764513" duration="2264000" />
      <workItem from="1749541051627" duration="6542000" />
      <workItem from="1749605348053" duration="705000" />
      <workItem from="1749606070277" duration="8433000" />
      <workItem from="1749623384909" duration="128000" />
      <workItem from="1749623592514" duration="8653000" />
    </task>
    <task id="LOCAL-00001" summary="fix：元模型发布时校验元模型管理员是否配置审批用户。&#10;tapd:&#10;【元模型审批角色未配置角色，会提交给admin用户，审批流程不会进行下一步】&#10;https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001083970">
      <option name="closed" value="true" />
      <created>1748239219228</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1748239219228</updated>
    </task>
    <task id="LOCAL-00002" summary="fix：修复选择矩阵和视图，搜索导致历史视图被搜索出来的bug。tapd：【143全局搜索跟我的空间数据不一致，部分视图点击提示视图已删除】&#10;                                     https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084432">
      <option name="closed" value="true" />
      <created>1748416229142</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1748416229142</updated>
    </task>
    <task id="LOCAL-00003" summary="fix：修复修改流程名称导致权限查询接口报错问题。tapd：【【流程管理】流程目录权限接口报错】&#10;                              https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084509">
      <option name="closed" value="true" />
      <created>1748416748201</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1748416748201</updated>
    </task>
    <task id="LOCAL-00004" summary="fix：修复修改流程名称导致权限查询接口报错问题。tapd：【【流程管理】流程目录权限接口报错】&#10;                              https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084509">
      <option name="closed" value="true" />
      <created>1748416881653</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1748416881653</updated>
    </task>
    <task id="LOCAL-00005" summary="fix：修复修改流程名称导致权限查询接口报错问题。tapd：【【流程管理】流程目录权限接口报错】&#10;                              https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084509">
      <option name="closed" value="true" />
      <created>1748416994320</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1748416994320</updated>
    </task>
    <task id="LOCAL-00006" summary="fix：修复修改流程名称导致权限查询接口报错问题。tapd:【143全局搜索跟我的空间数据不一致，部分视图点击提示视图已删除】&#10;                         https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084432">
      <option name="closed" value="true" />
      <created>1748420881075</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1748420881075</updated>
    </task>
    <task id="LOCAL-00007" summary="fix：解决初始化索引报错bug">
      <option name="closed" value="true" />
      <created>1748485932932</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1748485932932</updated>
    </task>
    <task id="LOCAL-00008" summary="fix：解决初始化索引报错bug。tapd：【【中信建投证券问题同步】信息资产管理分类列表配置保存时报错，首次选择时需要将所有字段勾选上，保存成功一次后面就不会再有问题】&#10;                      https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084323">
      <option name="closed" value="true" />
      <created>1748498499445</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1748498499445</updated>
    </task>
    <task id="LOCAL-00009" summary="fix：修改数据集未配置提示描述。tapd：【数据集停用/删除，架构地图及专题分析提示】&#10;                      https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084554">
      <option name="closed" value="true" />
      <created>1748503220087</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1748503220087</updated>
    </task>
    <task id="LOCAL-00010" summary="fix：修复er图转换报错bug。【ER图转换报错，跟端到端流程，批量保存报错类似】&#10;                 https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084540">
      <option name="closed" value="true" />
      <created>1748511735181</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1748511735181</updated>
    </task>
    <task id="LOCAL-00011" summary="fix：视图关系查询添加查询条件，分享情况下可以查询到。【用户A绘制视图V1无关系，v2有关系，视图分享给用户B,视图历史版本还原,B先将视图还原VI,再还原V2，还原后的视图，没有关系数据】&#10;                 https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084528">
      <option name="closed" value="true" />
      <created>1748513246783</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1748513246783</updated>
    </task>
    <task id="LOCAL-00012" summary="fix：修复关系列表无数据bug。tapd：【【关系管理】所属用户列无数据】&#10;                      https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084593">
      <option name="closed" value="true" />
      <created>1748517452315</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1748517452315</updated>
    </task>
    <task id="LOCAL-00013" summary="feat：新增关系数据检查接口，删除前调用，替换原来的searchRltByBean接口">
      <option name="closed" value="true" />
      <created>1748520109799</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1748520109799</updated>
    </task>
    <task id="LOCAL-00014" summary="fix：修改元模型初始化菜单。【【元模型】元模型重构】&#10;               https://www.tapd.cn/tapd_fe/33608965/story/detail/1133608965001129903">
      <option name="closed" value="true" />
      <created>1748571986611</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1748571986611</updated>
    </task>
    <task id="LOCAL-00015" summary="fix：修改对象管理初始化菜单。&#10;tapd:【【优化】优化对象管理和关系管理】&#10;     https://www.tapd.cn/tapd_fe/33608965/story/detail/1133608965001131069">
      <option name="closed" value="true" />
      <created>1748573223464</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1748573223464</updated>
    </task>
    <task id="LOCAL-00016" summary="refactor：优化初始化索引代码，使用推荐的写法重构">
      <option name="closed" value="true" />
      <created>1748949088230</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1748949088230</updated>
    </task>
    <task id="LOCAL-00017" summary="refactor：优化索引分片和index.mapping.total_fields.limit大小，将分类和关系相关索引设置为20000，并解决重启后索引重置为2000的bug">
      <option name="closed" value="true" />
      <created>1749017334705</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1749017334705</updated>
    </task>
    <task id="LOCAL-00018" summary="fix：1、解决成熟度评价修改关联资产不生成关系bug。2、解决因图片丢失为找到导致无法导出bug">
      <option name="closed" value="true" />
      <created>1749194537745</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1749194537745</updated>
    </task>
    <task id="LOCAL-00019" summary="fix：修复画布内搜索，只能前缀匹配搜索，无法从中间搜索的情况">
      <option name="closed" value="true" />
      <created>1749434990874</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1749434990874</updated>
    </task>
    <task id="LOCAL-00020" summary="fix：修复画布内搜索，只能前缀匹配搜索（私有库），无法从中间搜索的情况">
      <option name="closed" value="true" />
      <created>1749435405709</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1749435405709</updated>
    </task>
    <task id="LOCAL-00021" summary="feat：添加战略规划分析关系查询接口和根据分类code查询分类信息接口">
      <option name="closed" value="true" />
      <created>1749461655131</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1749461655131</updated>
    </task>
    <task id="LOCAL-00022" summary="feat：添加战略规划分析关系查询接口和根据分类code查询分类信息接口">
      <option name="closed" value="true" />
      <created>1749461701488</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1749461701488</updated>
    </task>
    <task id="LOCAL-00023" summary="feat：添加战略规划分析关系查询接口和根据分类code查询分类信息接口">
      <option name="closed" value="true" />
      <created>1749465734651</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1749465734651</updated>
    </task>
    <task id="LOCAL-00024" summary="feat：添加关系绘图接口&#10;tapd：【【画布】关系绘图】&#10;     https://www.tapd.cn/tapd_fe/33608965/story/detail/1133608965001131388">
      <option name="closed" value="true" />
      <created>1749534819604</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1749534819604</updated>
    </task>
    <task id="LOCAL-00025" summary="fix：关系绘图接口兼容未发布的ci，未发布直接返回空集合&#10;tapd：【【画布】关系绘图】&#10;     https://www.tapd.cn/tapd_fe/33608965/story/detail/1133608965001131388">
      <option name="closed" value="true" />
      <created>1749610555208</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1749610555208</updated>
    </task>
    <option name="localTasksCounter" value="26" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="66d3e40f-d21f-4ba7-b132-696d530bbb67" value="TOOL_WINDOW" />
        <entry key="a6ad7f11-904c-48ed-91ba-0640bdbc37ce" value="TOOL_WINDOW" />
        <entry key="54de90ed-6253-41ab-9f9c-8d6b46ec6625" value="TOOL_WINDOW" />
        <entry key="c8ab94fc-ce1e-4113-becf-0e4444bf8b2b" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="develop-v6.5.0-jdk17-spring3.4.5" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="54de90ed-6253-41ab-9f9c-8d6b46ec6625">
          <value>
            <State>
              <option name="CUSTOM_BOOLEAN_PROPERTIES">
                <map>
                  <entry key="Show.Git.Branches" value="true" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="develop-v6.5.0-lichong" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="66d3e40f-d21f-4ba7-b132-696d530bbb67">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="user">
                    <value>
                      <list>
                        <option value="*" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="develop-v6.5.0-jdk17-spring3.4.5" />
                      </list>
                    </value>
                  </entry>
                  <entry key="text">
                    <value>
                      <list>
                        <option value="关系" />
                      </list>
                    </value>
                  </entry>
                  <entry key="user">
                    <value>
                      <list>
                        <option value="*" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="a6ad7f11-904c-48ed-91ba-0640bdbc37ce">
          <value>
            <State>
              <option name="CUSTOM_BOOLEAN_PROPERTIES">
                <map>
                  <entry key="Show.Git.Branches" value="true" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="develop-v6.5.0-jdk17-spring3.4.5" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="c8ab94fc-ce1e-4113-becf-0e4444bf8b2b">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="fix：元模型发布时校验元模型管理员是否配置审批用户。&#10;tapd:&#10;【元模型审批角色未配置角色，会提交给admin用户，审批流程不会进行下一步】&#10;https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001083970" />
    <MESSAGE value="fix：修复选择矩阵和视图，搜索导致历史视图被搜索出来的bug。tapd：【143全局搜索跟我的空间数据不一致，部分视图点击提示视图已删除】&#10;                                     https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084432" />
    <MESSAGE value="fix：修复修改流程名称导致权限查询接口报错问题。tapd：【【流程管理】流程目录权限接口报错】&#10;                              https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084509" />
    <MESSAGE value="fix：修复修改流程名称导致权限查询接口报错问题。tapd:【143全局搜索跟我的空间数据不一致，部分视图点击提示视图已删除】&#10;                         https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084432" />
    <MESSAGE value="fix：解决初始化索引报错bug" />
    <MESSAGE value="fix：解决初始化索引报错bug。tapd：【【中信建投证券问题同步】信息资产管理分类列表配置保存时报错，首次选择时需要将所有字段勾选上，保存成功一次后面就不会再有问题】&#10;                      https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084323" />
    <MESSAGE value="fix：修改数据集未配置提示描述。tapd：【数据集停用/删除，架构地图及专题分析提示】&#10;                      https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084554" />
    <MESSAGE value="es17" />
    <MESSAGE value="fix：修复er图转换报错bug。【ER图转换报错，跟端到端流程，批量保存报错类似】&#10;                 https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084540" />
    <MESSAGE value="fix：视图关系查询添加查询条件，分享情况下可以查询到。【用户A绘制视图V1无关系，v2有关系，视图分享给用户B,视图历史版本还原,B先将视图还原VI,再还原V2，还原后的视图，没有关系数据】&#10;                 https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084528" />
    <MESSAGE value="fix：修复关系列表无数据bug。tapd：【【关系管理】所属用户列无数据】&#10;                      https://www.tapd.cn/tapd_fe/33608965/bug/detail/1133608965001084593" />
    <MESSAGE value="feat：新增关系数据检查接口，删除前调用，替换原来的searchRltByBean接口" />
    <MESSAGE value="fix：修改元模型初始化菜单。【【元模型】元模型重构】&#10;               https://www.tapd.cn/tapd_fe/33608965/story/detail/1133608965001129903" />
    <MESSAGE value="fix：修改对象管理初始化菜单。&#10;tapd:【【优化】优化对象管理和关系管理】&#10;     https://www.tapd.cn/tapd_fe/33608965/story/detail/1133608965001131069" />
    <MESSAGE value="修复es创建索引warn日志" />
    <MESSAGE value="refactor：优化初始化索引代码，使用推荐的写法重构" />
    <MESSAGE value="refactor：优化索引分片和index.mapping.total_fields.limit大小，将分类和关系相关索引设置为20000，并解决重启后索引重置为2000的bug" />
    <MESSAGE value="fix：1、解决成熟度评价修改关联资产不生成关系bug。2、解决因图片丢失为找到导致无法导出bug" />
    <MESSAGE value="fix：修复画布内搜索，只能前缀匹配搜索，无法从中间搜索的情况" />
    <MESSAGE value="fix：修复画布内搜索，只能前缀匹配搜索（私有库），无法从中间搜索的情况" />
    <MESSAGE value="feat：添加战略规划分析关系查询接口和根据分类code查询分类信息接口" />
    <MESSAGE value="feat：添加关系绘图接口&#10;tapd：【【画布】关系绘图】&#10;     https://www.tapd.cn/tapd_fe/33608965/story/detail/1133608965001131388" />
    <MESSAGE value="fix：关系绘图接口兼容未发布的ci，未发布直接返回空集合&#10;tapd：【【画布】关系绘图】&#10;     https://www.tapd.cn/tapd_fe/33608965/story/detail/1133608965001131388" />
    <option name="LAST_COMMIT_MESSAGE" value="fix：关系绘图接口兼容未发布的ci，未发布直接返回空集合&#10;tapd：【【画布】关系绘图】&#10;     https://www.tapd.cn/tapd_fe/33608965/story/detail/1133608965001131388" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-service/1.0.0-SNAPSHOT/uino-eam-micro-service-1.0.0-SNAPSHOT-sources.jar!/com/uino/service/license/microservice/impl/LicenseAuthSvc.java</url>
          <line>122</line>
          <option name="timeStamp" value="10" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-service/1.0.0-SNAPSHOT/uino-eam-micro-service-1.0.0-SNAPSHOT-sources.jar!/com/uino/service/license/microservice/impl/LicenseAuthSvc.java</url>
          <line>359</line>
          <option name="timeStamp" value="19" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-web/1.0.0-SNAPSHOT/uino-eam-micro-web-1.0.0-SNAPSHOT-sources.jar!/com/uino/init/UinoLicenseAuthorityFilter.java</url>
          <line>36</line>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-web/1.0.0-SNAPSHOT/uino-eam-micro-web-1.0.0-SNAPSHOT-sources.jar!/com/uino/init/UinoLicenseAuthorityFilter.java</url>
          <line>59</line>
          <option name="timeStamp" value="21" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/asset/impl/AssetListAttrConfSvcImpl.java</url>
          <line>81</line>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/asset/impl/AssetListAttrConfSvcImpl.java</url>
          <line>78</line>
          <option name="timeStamp" value="24" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/asset/impl/AssetListAttrConfSvcImpl.java</url>
          <line>77</line>
          <option name="timeStamp" value="26" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/impl/FxDiagramSvcImpl.java</url>
          <line>1234</line>
          <option name="timeStamp" value="49" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/impl/FxDiagramSvcImpl.java</url>
          <line>1208</line>
          <option name="timeStamp" value="50" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/impl/FxDiagramSvcImpl.java</url>
          <line>1363</line>
          <option name="timeStamp" value="51" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/impl/CIRltExtendSvcImpl.java</url>
          <line>998</line>
          <option name="timeStamp" value="54" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/impl/CIRltExtendSvcImpl.java</url>
          <line>1003</line>
          <option name="timeStamp" value="55" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/impl/CIRltExtendSvcImpl.java</url>
          <line>1008</line>
          <option name="timeStamp" value="56" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-dao/1.0.0-SNAPSHOT/uino-eam-micro-dao-1.0.0-SNAPSHOT-sources.jar!/com/uino/dao/AbstractESBaseDao.java</url>
          <line>962</line>
          <option name="timeStamp" value="60" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-web/1.0.0-SNAPSHOT/uino-eam-micro-web-1.0.0-SNAPSHOT-sources.jar!/com/uino/init/ControllerAspect.java</url>
          <line>162</line>
          <option name="timeStamp" value="75" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-web/1.0.0-SNAPSHOT/uino-eam-micro-web-1.0.0-SNAPSHOT-sources.jar!/com/uino/init/ControllerAspect.java</url>
          <line>166</line>
          <option name="timeStamp" value="76" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/bm/impl/QuickDrawing.java</url>
          <line>364</line>
          <option name="timeStamp" value="81" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/bm/impl/QuickDrawing.java</url>
          <line>443</line>
          <option name="timeStamp" value="82" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/bm/impl/QuickDrawing.java</url>
          <line>514</line>
          <option name="timeStamp" value="83" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/bm/impl/QuickDrawing.java</url>
          <line>441</line>
          <properties>
            <option name="lambda-ordinal" value="-1" />
          </properties>
          <option name="timeStamp" value="85" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/bm/impl/QuickDrawing.java</url>
          <line>434</line>
          <option name="timeStamp" value="86" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/bm/impl/QuickDrawing.java</url>
          <line>420</line>
          <option name="timeStamp" value="87" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/bm/impl/QuickDrawing.java</url>
          <line>429</line>
          <option name="timeStamp" value="88" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/bm/impl/QuickDrawing.java</url>
          <line>440</line>
          <option name="timeStamp" value="89" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/bm/impl/QuickDrawing.java</url>
          <line>556</line>
          <option name="timeStamp" value="90" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/bm/impl/QuickDrawing.java</url>
          <line>557</line>
          <properties>
            <option name="lambda-ordinal" value="-1" />
          </properties>
          <option name="timeStamp" value="91" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/bm/impl/QuickDrawing.java</url>
          <line>563</line>
          <option name="timeStamp" value="92" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/bm/impl/QuickDrawing.java</url>
          <line>425</line>
          <option name="timeStamp" value="93" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/es/IamsESCmdbCommDesignSvc.java</url>
          <line>991</line>
          <option name="timeStamp" value="105" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-web/1.0.0-SNAPSHOT/uino-eam-micro-web-1.0.0-SNAPSHOT.jar!/com/uino/web/sys/mvc/DictionaryMvc.class</url>
          <line>128</line>
          <option name="timeStamp" value="106" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/utils/Base64Utils.java</url>
          <line>93</line>
          <option name="timeStamp" value="113" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/utils/Base64Utils.java</url>
          <line>88</line>
          <option name="timeStamp" value="115" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/utils/Base64Utils.java</url>
          <line>85</line>
          <option name="timeStamp" value="119" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-web/1.0.0-SNAPSHOT/uino-eam-micro-web-1.0.0-SNAPSHOT-sources.jar!/com/uino/init/ControllerAspect.java</url>
          <line>121</line>
          <option name="timeStamp" value="120" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-web/1.0.0-SNAPSHOT/uino-eam-micro-web-1.0.0-SNAPSHOT-sources.jar!/com/uino/init/ControllerAspect.java</url>
          <line>124</line>
          <option name="timeStamp" value="121" />
        </line-breakpoint>
        <breakpoint enabled="true" type="java-exception">
          <properties class="org.springframework.http.converter.HttpMessageNotReadableException" package="org.springframework.http.converter" />
          <option name="timeStamp" value="107" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>