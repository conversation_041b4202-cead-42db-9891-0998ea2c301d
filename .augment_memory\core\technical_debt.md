# EAM系统 - 技术债务清单

## Elasticsearch相关问题

### 1. ES 7.10.2兼容性问题
- **问题**: mapper_parsing_exception，analyzer参数应用到long类型字段
- **影响**: 索引创建失败，数据无法正常存储
- **状态**: 已修复 - 使用org.elasticsearch.common.Strings.toString()替代XContentBuilder.toString()
- **遗留**: 动态模板仍可能对数字类型错误添加analyzer参数

### 2. 废弃参数问题
- **问题**: include_type_name参数在ES 7.x中已废弃
- **影响**: 创建索引时出现废弃警告
- **建议**: 重构AbstractESBaseDao以兼容ES 7.x
- **优先级**: 中等

### 3. 客户端迁移需求
- **问题**: Elasticsearch High Level Rest Client已废弃
- **建议**: 迁移到Elasticsearch Java API Client
- **影响**: 长期维护性和性能优化
- **优先级**: 低

### 4. 索引字段限制
- **问题**: index.mapping.total_fields.limit默认2000，部分索引需要20000
- **解决方案**: 仅对特定索引设置高限制，避免全局配置
- **状态**: 需要实施

## 数据访问层问题

### 1. Matrix数据过滤
- **问题**: tableId字段存在但历史版本过滤逻辑不准确
- **影响**: 可能返回历史版本数据
- **状态**: 需要改进过滤策略

### 2. 日索引管理
- **问题**: AbstractSplitBaseDao实现创建过多日索引
- **建议**: 寻找替代索引管理策略
- **优先级**: 中等

## 架构优化建议

### 1. 服务拆分优化
- 评估当前微服务边界合理性
- 考虑AI服务独立部署
- 优化服务间通信效率

### 2. 性能优化
- Elasticsearch查询性能优化
- 数据库连接池配置优化
- AI服务调用超时和缓存策略

### 3. 监控完善
- 增强AI服务监控
- 完善Elasticsearch集群监控
- 建立性能基线和告警机制

## 修复优先级
1. **高**: ES动态模板数字类型analyzer问题
2. **中**: include_type_name参数废弃问题
3. **中**: 索引字段限制配置
4. **中**: Matrix数据过滤逻辑
5. **低**: ES客户端迁移
