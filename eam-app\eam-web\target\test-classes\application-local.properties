#spring.profiles.include=minor
spring.config.import=application-minor.properties
#声明循环依赖
spring.main.allow-circular-references=true

#服务的访问路径
server.servlet.context-path=/tarsier-eam

#=====================================Nacos相关配置=====================================
# 是否开启配置中心
spring.cloud.nacos.config.enabled=false
# 是否开启服务注册调用
spring.cloud.nacos.discovery.enabled=true
# 注册中心地址
spring.cloud.nacos.config.server-addr=10.100.33.193
# 注册中心分组
spring.cloud.nacos.discovery.group=DEFAULT_GROUP
spring.cloud.nacos.discovery.namespace=6e7a926e-19ad-4838-b3fe-4e0504cf71fb
# 注册中心namespace配置
spring.cloud.nacos.discovery.server-addr=${spring.cloud.nacos.config.server-addr}
# 注册中心账号密码
spring.cloud.nacos.discovery.username=nacos
spring.cloud.nacos.discovery.password=nacos

#=====================================redis相关配置=====================================
# Redis服务器地址
spring.data.redis.host=**************
# Redis服务器连接端口
spring.data.redis.port=6379
#spring.data.redis.cluster.nodes=*************:6379,*************:6380,**************:6379,**************:6380,**************:6379,**************:6380
# Redis数据库索引（默认为0）
spring.data.redis.database=0
# Redis服务器连接密码（默认为空）
spring.data.redis.password=Uinnova@123
# 连接超时时间（毫秒）
spring.data.redis.timeout=10000
redis.enable=true
#redis.address=redis://${spring.data.redis.host}:${spring.data.redis.port}
#支持single、master、sentinel、cluster模式，默认single
redis.type=
redis.password=${spring.data.redis.password}

# 自动刷新redis集群
spring.data.redis.lettuce.cluster.refresh.adaptive=true
spring.data.redis.lettuce.cluster.refresh.period=30000

#=====================================elasticsearch相关配置=====================================
##ElasticSearch地址
esIps=*************:29200
##ElasticSearch数据库用户名
esUser=uinnova
##ElasticSearch数据库密码
esPwd=ENC(p9nZzfKfCVu9bDHgBRy/dGo7L8OKQXP+)
##ElasticSearch是否开启认证
isAuth=false

#=====================================mysql相关配置=====================================
#Mysql数据库地址
ds.jdbc.vmdb.url=************************************************************************************************************************************************
ds.jdbc.vmdb.user=uinnova
ds.jdbc.vmdb.passwd=ENC(JiIIQjp80jzaI3qohk9rzGuQBv5mBFiW)

#=====================================资源地址相关配置=====================================
#本地资源http服务地址
http.resource.space=http://**************/rsm
#本地资源存储地址
local.resource.space=/uinnova/uino/rsm

#二级菜单默认图相对路径
local.resource.pic.url =/122/defaultIcon/default_menu_pic.png
local.web.resource.space = /home/<USER>/quickea/

#======================================对象存储相关配置=====================================
#对象存储
spring.cloud.obs.endpoint=oss-cn-beijing.aliyuncs.com
# 对象存储访问ak
spring.cloud.obs.access-key=testAK
# 对象存储访问 sk
spring.cloud.obs.secret-key=testSK
# 对象存储桶名称
spring.cloud.obs.bucketName=quickea
# 对象存储资源访问链接的签名有效期（s）
spring.cloud.obs.urlExpireSeconds=3600
# 对象存储region
spring.cloud.obs.region=cn-beijing
spring.cloud.obs.isHttps=N
# 实现对象存储的操作的SDK（huawei：华为官方提供的sdk，其余默认为阿里云sdk）
rsm.util.sdkType=test
# 当前环境是否使用对象存储
obs.use=false
# 资源路径前缀（后台部分功能，处理资源的路径时，并未使用http.resource.space配置，而是在路径前拼接了“/rsm”，对于这部分功能需要使用该配置处理返回给前台的资源路径）。（新增配置，非必需。不配置时默认值为：/tarsier-eam/rsm）
obs.rsm.url.prefix=/tarsier-eam/rsm

#=====================================登录鉴权相关配置=====================================
#登录方式：sso-扫码、短信、thingjs, thingjs-只支持thingjs登录， oauth-用户密码方式登录
monet.login.loginMethod = oauth

#spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration, org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration
#oauth鉴权相关配置
oauth.open=true
oauth.client.id=tarsier-eam
oauth.client.secret=secret
oauth.client.grant_type=authorization_code
oauth.server.url=http://**************/oauth
oauth.server.in_url=http://**************/oauth
oauth.server.token_callback.url=http://**************/tarsier-eam/getTokenByCode
#权限模块http server path
permission.http.prefix=http://**************:1515/tarsier-eam
#License服务地址
project.license.register.url=http://**************/examples/#/license

#=====================================项目功能相关配置=====================================
#调用公共组件的服务方式
base.load-type=local
#对象管理勾选业务主键数量限制
uino.base.ci.primarykey.maxcount=5
#日志保留时长，单位：天
uino.log.clear.duration=7
#指标单位
kpi.units=度,斤
#颁发token时是否携带随机参数
oauth.token.random_param=false
uino.monitor.ep.exist=false
uino.monitor.event.send.url=
#配置对象管理显示库 // 私有库:PRIVATE，设计库：DESIGN，运行库/基线库：BASELINE。不配置默认为BASELINE
uino.eam.lib_type.show=PRIVATE,DESIGN,BASELINE
#允许跨域请求的主机地址
uino.eam.allowed.origins=*
#文档上传正则表达式
uino.eam.word_name_regex=.*[.](?i)(doc|docx|xls|xlsx|ppt|pptx|pdf|txt|jpg|jpeg|bmp|gif|png|ico|swf|eot|svg|ttf|woff|woff2)

#在线开发返回前台json保存路径
monet.exportJson.exportPath = /topo/rsm
#在topobuilder环境为true，其它默认为false
monet.use.topobuilder.thingjs=false
#数据超市使用的三库标识
uino.cmdb.libtype=DESIGN
#上传文件大小之和
total.file.size=524288000
#自动成图-部署架构图排序(DMZ-WEB-APP-DB)
auto.deployment.arch.diagram.sort=0-1-2-3
# license是否开启
uino.license=false
# 初始化加载索引
init.data.action=true
# 自动成图缓存时间
uino.auto.draw.cache.timeout=60000
# 数据建模C'实体发布后推送应用子系统相关数据到DIX(默认false不开启推送),以及dix接收数据接口
uino.eam.sync.system.open=false
uino.eam.sync.dix.url=http://10.100.52.116/dix/sync

process.definition.key=cj_technical_scheme_approve
uino.eam.word.chapter_name_regex=.*[.](?i)(doc|docx|xls|xlsx|ppt|pptx|pdf|jpg|jpeg|png|bmp)
# xss漏洞相关配置
mica.xss.mode=CLEAR
mica.xss.enable-escape=true
# 成熟度计算规则 {分类属性名称}拼接计算规则（加减乘除）
maturity.calculate.rule={角色现状得分}*0.8+{流程现状得分}*1.2+{数据现状得分}+{IT现状得分}*0.25
# 成熟度匹配规则 成熟度字典主键名称:判断规则（X表示得分值，配置大于、小于、等于条件）
maturity.match.rule=缺失:X <= 3.333,中等:X > 3.333 && X <= 6.67,强:X > 6.67
# 成熟度属性名 默认名称“成熟度”
maturity.attribute.name=成熟度

# 关系遍历批处理任务的执行周期(cron表达式)
batch.process.relation.rule.cron=0 0 3 * * ?
# 是否开启关系自动构建任务
batch.process.relation.rule.job.enable=true

# Dify相关配置
dify.diagram.api.url = http://10.100.30.20:8080/v1/workflows/run
dify.diagram.api.key = app-gxt4INstDAUfpAKF1efoH3YB
dify.diagram.api.user = QuickEA
uino.eam.flow.init.open=false