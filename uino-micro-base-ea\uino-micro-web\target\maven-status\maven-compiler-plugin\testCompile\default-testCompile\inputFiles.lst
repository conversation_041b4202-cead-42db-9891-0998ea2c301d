D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\sys\SysCIOperateLogMvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\org\svc\InterchangeOrgNoTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\org\svc\UserExtentOrgRoleTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\org\mvc\GetOrgTreeTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\monitor\PerformanceTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\role\svc\AddUserMenuRltTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\sys\SysOperateLogMvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\image\svc\ReplaceImageTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\role\SysRoleMvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\user\UserSvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\tag\TagSvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\user\UserMvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\run\RunLocal.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\org\mvc\GetUserIdsTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\ci_rlt\mvc\BindCiRltTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\role\svc\AddUserDataModuleRltTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\org\mvc\AddUsersTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\org\mvc\RemoveUsersTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\run\RunLocalDev.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\org\mvc\AddRolesTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\resource\svc\UpdatePublicUrlTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\rlt_class\mvc\DeleteByIdsTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\org\mvc\GetRoleIdsTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\ci_rlt\svc\DelRltByCiIdsTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\dir\DirSvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\image\svc\ImportZipImageTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\rlt_class\mvc\GetRltClassByIdTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\ci_rlt\mvc\ImportCiRltTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\rlt_class\mvc\SaveOrUpdateTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\run\RunLocal79.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\ci_rlt\mvc\UpdateCiRltAttr.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\ci\CISvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\sys\CIOperateLogSvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\ci_rlt\mvc\ClearRltByClassIdTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\org\mvc\DeleteByIdsTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\ci_class\CiClassMvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\org\mvc\RemoveRolesTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\tag\TagMvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\sys\OperateLogSvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\monitor\EventTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\encrpyt\DigestTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\ci\CiMvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\ci_class\CIClassSvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\role\svc\AddDataModuleMenuTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\image\svc\ExportImageZipByDirIdTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\encrpyt\EncryptTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\ci\CIHistorySvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\run\RunLocalDev79.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\ci_rlt\mvc\ExportCiRltTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\Test.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\image\svc\ImportImageTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\dir\DirMvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\role\ImgMvcTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\run\RunLocal143.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\ci_rlt\mvc\DelRltByCiIdTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\ci_rlt\mvc\SearchRltByBeanTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\role\svc\GetRolesByOrgIdTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\org\mvc\SaveOrUpdateOrgTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\run\RunLocalDev143.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\cmdb\rlt_class\mvc\QueryAllClassesTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\org\svc\QueryOrgTest.java
D:\workspace\mycode\ea\uino-micro-base-ea\uino-micro-web\src\test\java\com\uino\run\RunRpc.java
