<?xml version="1.0" encoding="UTF-8"?>
<event-model version="1.0" description="事件模型" table="mon_eap_event" memoryTable="mon_eap_event_memory" partitionColumn="LASTOCCURRENCE">

    <field>
        <field-name>id</field-name>
        <title>事件序列号</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>identifier</field-name>
        <title>事件标识</title>
        <data-type>varchar(512)</data-type>
        <default-value/>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>serial</field-name>
        <title>事件序列号</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>ciName</field-name>
        <title>事件对象</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>true</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>ciCode</mmdb-key>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>ciPrimaryKey</field-name>
        <title>配置项业务主键</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>true</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>ciPrimaryKey</mmdb-key>
    </field>

    <field>
        <field-name>severity</field-name>
        <title>事件级别</title>
        <data-type>integer</data-type>
        <default-value>1</default-value>
        <required>true</required>
        <selectable>true</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>true</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>summary</field-name>
        <title>事件描述</title>
        <data-type>varchar(2048)</data-type>
        <default-value>NA</default-value>
        <required>true</required>
        <selectable>true</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>true</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>eventTitle</field-name>
        <title>事件标题</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>true</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>true</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>status</field-name>
        <title>事件状态</title>
        <data-type>integer</data-type>
        <default-value>1</default-value>
        <required>true</required>
        <selectable>true</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>ciCategoryName</field-name>
        <title>事件对象分类</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>true</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>categoryName</mmdb-key>
    </field>

    <field>
        <field-name>firstOccurrence</field-name>
        <title>首次发生时间</title>
        <data-type>timestamp</data-type>
        <default-value/>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>lastOccurrence</field-name>
        <title>最后发生时间</title>
        <data-type>timestamp</data-type>
        <default-value/>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>true</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>stateChange</field-name>
        <title>事件变化时间</title>
        <data-type>timestamp</data-type>
        <default-value/>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>tally</field-name>
        <title>次数</title>
        <data-type>integer</data-type>
        <default-value>1</default-value>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>acknowledged</field-name>
        <title>是否已确认</title>
        <data-type>integer</data-type>
        <default-value>0</default-value>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>grade</field-name>
        <title>是否已升级</title>
        <data-type>integer</data-type>
        <default-value>0</default-value>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>serverName</field-name>
        <title>服务器名称</title>
        <data-type>varchar(64)</data-type>
        <default-value></default-value>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>serverSerial</field-name>
        <title>服务器事件序列号</title>
        <data-type>varchar(64)</data-type>
        <default-value>1</default-value>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>sourceId</field-name>
        <title>事件来源编号</title>
        <data-type>integer</data-type>
        <default-value>0</default-value>
        <required>true</required>
        <selectable>true</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>sourceName</field-name>
        <title>事件来源名称</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>true</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>sourceIdentifier</field-name>
        <title>源事件标识</title>
        <data-type>varchar(512)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>sourceEventId</field-name>
        <title>源事件序列号</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>true</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>sourceCiName</field-name>
        <title>源对象名称</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>true</required>
        <selectable>true</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>sourceAlertKey</field-name>
        <title>源指标名称</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>true</required>
        <selectable>true</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>sourceSeverity</field-name>
        <title>源事件级别</title>
        <data-type>varchar(20)</data-type>
        <default-value/>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>true</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>sourceSummary</field-name>
        <title>源事件描述</title>
        <data-type>varchar(1024)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>true</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>true</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>ackInfo</field-name>
        <title>确认事件信息</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>ackTime</field-name>
        <title>确认事件时间</title>
        <data-type>datetime</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>ackUId</field-name>
        <title>确认事件用户标识</title>
        <data-type>varchar(64)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>closeInfo</field-name>
        <title>关闭事件信息</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>closeTime</field-name>
        <title>关闭事件时间</title>
        <data-type>datetime</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>closeUId</field-name>
        <title>关闭事件用户标识</title>
        <data-type>varchar(64)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>kpiId</field-name>
        <title>事件指标ID</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>KPI</source-type>
        <mmdb-key>id</mmdb-key>
    </field>

    <field>
        <field-name>kpiName</field-name>
        <title>事件指标名称</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>KPI</source-type>
        <mmdb-key>name</mmdb-key>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>kpiCategoryID</field-name>
        <title>事件指标分类ID</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>KPI</source-type>
        <mmdb-key>kpiCategoryId</mmdb-key>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>kpiCategoryName</field-name>
        <title>事件指标分类名称</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>KPI</source-type>
        <mmdb-key>kpiCategoryName</mmdb-key>
    </field>

    <field>
        <field-name>kpiDescription</field-name>
        <title>事件指标描述信息</title>
        <data-type>varchar(512)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>KPI</source-type>
        <mmdb-key>description</mmdb-key>
    </field>

    <field>
        <field-name>kpiType</field-name>
        <title>事件指标二级分类</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>KPI</source-type>
        <mmdb-key>kpiCategoryName</mmdb-key>
    </field>

    <field>
        <field-name>kpiItem</field-name>
        <title>事件指标三级分类</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>KPI</source-type>
        <mmdb-key>item</mmdb-key>
    </field>

    <field>
        <field-name>kpiDomain</field-name>
        <title>事件指标域</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>KPI</source-type>
        <mmdb-key>domain</mmdb-key>
    </field>

    <field>
        <field-name>kpiUnit</field-name>
        <title>指标参数单位</title>
        <data-type>varchar(32)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>KPI</source-type>
        <mmdb-key>unit</mmdb-key>
    </field>

    <field>
        <field-name>kpiInstance</field-name>
        <title>事件指标实例</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>KPI</source-type>
        <mmdb-key>instance</mmdb-key>
    </field>

    <field>
        <field-name>ciId</field-name>
        <title>事件对象ID</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>id</mmdb-key>
        <redis-search-key>true</redis-search-key>
    </field>


    <field>
        <field-name>ciCategoryID</field-name>
        <title>事件对象分类ID</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>categoryId</mmdb-key>
    </field>

    <field>
        <field-name>ciType</field-name>
        <title>事件对象二级分类</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>categoryName</mmdb-key>
    </field>

    <field>
        <field-name>ciItem</field-name>
        <title>事件对象三级分类</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>item</mmdb-key>
    </field>

    <field>
        <field-name>ciApplication</field-name>
        <title>对象所属应用系统</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>application</mmdb-key>
    </field>

    <field>
        <field-name>ciOwner</field-name>
        <title>配置项负责人</title>
        <data-type>varchar(64)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>owner</mmdb-key>
    </field>

    <field>
        <field-name>ciMgmtTeam</field-name>
        <title>配置项管理部室</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>mgmtTeam</mmdb-key>
    </field>

    <field>
        <field-name>ciDatacenter</field-name>
        <title>配置项数据中心</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>datacenter</mmdb-key>
    </field>

    <field>
        <field-name>ciLocation</field-name>
        <title>配置项物理位置</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>location</mmdb-key>
    </field>

    <field>
        <field-name>ciStatus</field-name>
        <title>配置项状态</title>
        <data-type>varchar(64)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>status</mmdb-key>
    </field>

    <field>
        <field-name>ciUsageType</field-name>
        <title>配置项应用类型</title>
        <data-type>varchar(64)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>usageType</mmdb-key>
    </field>

    <field>
        <field-name>ciMgmtGroup</field-name>
        <title>配置项群组</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>mgmtGroup</mmdb-key>
    </field>

    <field>
        <field-name>ciDeployUnit</field-name>
        <title>配置项部署单元</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>CI</source-type>
        <mmdb-key>deployUnit</mmdb-key>
    </field>

    <field>
        <field-name>blackout</field-name>
        <title>事件屏蔽标识</title>
        <data-type>integer</data-type>
        <default-value>0</default-value>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>alarmSMS</field-name>
        <title>事件短信通知标识</title>
        <data-type>integer</data-type>
        <default-value>0</default-value>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>alarmEmail</field-name>
        <title>事件邮件通知标识</title>
        <data-type>integer</data-type>
        <default-value>0</default-value>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>alarmTicket</field-name>
        <title>事件创建自动工单标识</title>
        <data-type>integer</data-type>
        <default-value>0</default-value>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>alarmWorkflow</field-name>
        <title>事件调用自动工作流标识</title>
        <data-type>integer</data-type>
        <default-value>0</default-value>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>duplicateSerial</field-name>
        <title>重复事件首次序列号</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>maPeriodId</field-name>
        <title>维护期ID</title>
        <data-type>varchar(128)</data-type>
        <default-value>0</default-value>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>filterType</field-name>
        <title>过滤类型</title>
        <data-type>integer</data-type>
        <default-value>0</default-value>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
        <redis-search-key>true</redis-search-key>
    </field>

    <field>
        <field-name>scene</field-name>
        <title>场景</title>
        <data-type>varchar(128)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>
    <field>
        <field-name>viewId</field-name>
        <title>视图</title>
        <data-type>varchar(64)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>
    <field>
        <field-name>ifNotify</field-name>
        <title>是否发送通知</title>
        <data-type>integer</data-type>
        <default-value>0</default-value>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>
    <field>
        <field-name>duration</field-name>
        <title>持续时间</title>
        <data-type>varchar(128)</data-type>
        <default-value>0</default-value>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>
    <field>
        <field-name>tag</field-name>
        <title>标签</title>
        <data-type>integer</data-type>
        <default-value>0</default-value>
        <required>true</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>
    <field>
        <field-name>shieldRecoverTime</field-name>
        <title>屏蔽恢复时间</title>
        <data-type>map</data-type>
        <default-value>0</default-value>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
        <mmdb-key/>
    </field>

    <field>
        <field-name>DTOwnedSpace</field-name>
        <title>DT所属空间</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
    </field>

    <field>
        <field-name>DTPhysicallocation</field-name>
        <title>地理位置</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
    </field>

    <field>
        <field-name>DTName</field-name>
        <title>孪生体名称</title>
        <data-type>varchar(256)</data-type>
        <default-value/>
        <required>false</required>
        <selectable>false</selectable>
        <redefinable>false</redefinable>
        <update-on-deduplicate>false</update-on-deduplicate>
        <source-type>EVENT</source-type>
    </field>
</event-model>
