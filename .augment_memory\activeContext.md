# EAM企业架构管理系统 - 活跃上下文

## 当前项目状态
- **项目名称**: EAM企业架构管理系统
- **版本**: fuxi-1.0.0-SNAPSHOT
- **架构**: Spring Boot 3.x微服务架构
- **主要技术栈**: Java 17, Spring Boot 3.4.5, Spring Cloud 2024.0.1, Elasticsearch 7.17.x, MySQL 8.0

## 项目结构
```
ea/
├── eam-app/                    # EAM主应用模块
│   ├── eam-api/               # API接口定义
│   ├── eam-base/              # 基础组件
│   ├── eam-db/                # 数据库操作
│   ├── eam-service/           # 业务服务(AI/DM/CJ)
│   ├── eam-web/               # Web控制器
│   ├── eam-workable/          # 工作流模块
│   └── eam-feign-*/           # Feign服务
└── uino-micro-base-ea/        # 微服务基础框架
    ├── uino-micro-dao/        # 数据访问层
    ├── uino-micro-service/    # 服务实现
    ├── uino-micro-web/        # Web服务
    └── uino-micro-*/          # 其他微服务组件
```

## 核心功能模块
1. **AI智能分析**: 基于Dify工作流的AI绘图和数据分析
2. **专题分析**: 矩阵分析、关系图谱、接口服务分析
3. **数据建模**: 概念/逻辑/物理建模，支持DDL生成
4. **企业架构管理**: 业务/应用/数据/技术架构管理
5. **工作流管理**: 基于Flowable的BPMN 2.0流程引擎
6. **架构资产管理**: 多库管理(设计库/基线库/私有库)

## 已知技术问题
1. **Elasticsearch兼容性**: 
   - ES 7.10.2升级导致mapper_parsing_exception
   - 动态模板对数字类型错误添加analyzer参数
   - include_type_name参数已废弃
   - 需要迁移到新的Elasticsearch Java API Client

2. **索引管理**:
   - index.mapping.total_fields.limit默认2000，需要特定索引设置为20000
   - AbstractSplitBaseDao日索引创建策略需要优化

3. **Matrix数据过滤**:
   - tableId字段存在但历史版本过滤逻辑需要改进

## 当前工作重点
- 系统初始化和记忆系统建立
- 技术债务识别和解决方案规划
- 代码质量和架构优化

## 开发偏好
- 不需要编写测试用例
- 使用包管理器而非手动编辑配置文件
- 优先使用MCP服务进行信息收集和反馈

## 最后更新
- 时间: 项目初始化
- 操作: augment_init命令执行
- 状态: 记忆系统已建立
