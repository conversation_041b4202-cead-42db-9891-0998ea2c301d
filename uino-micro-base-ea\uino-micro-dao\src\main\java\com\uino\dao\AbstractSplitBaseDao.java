package com.uino.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.util.ESUtil;
import com.uino.util.exception.LoginException;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.nio.entity.NStringEntity;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.admin.indices.alias.get.GetAliasesRequest;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.admin.indices.settings.put.UpdateSettingsRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.*;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.cluster.metadata.AliasMetadata;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.xcontent.XContentBuilder;
import org.elasticsearch.xcontent.XContentFactory;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.histogram.*;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.*;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 按时间切割索引的es操作类
 *
 * <AUTHOR>
 * @date 2019年9月23日 下午2:11:38
 */
@Slf4j
@RefreshScope
public abstract class AbstractSplitBaseDao {

    protected static final String STATS_AGG_MAX = "max";
    protected static final String STATS_AGG_MIN = "min";
    protected static final String STATS_AGG_AVG = "avg";
    protected static final String STATS_AGG_SUM = "sum";

    @Resource
    private RestHighLevelClient client;

    @Value("${data.scope:}")
    private String dataScope;

    /**
     * 默认分索引分割时间格式
     */
    protected final static String DEFAULT_DATE_FORMAT = "yyyyMM";

    /**
     * 索引缓存 key=索引前缀 value=索引列表
     */
    protected final Map<String, List<String>> INDICES_MAP = new ConcurrentHashMap<>();

    /**
     * 获取索引前缀
     */
    public abstract String getIndexPrefix();

    /**
     * 获取索引信息
     *
     * @return
     */
    protected final String getFullIndexPrefix() {
        return getDataScope() + getIndexPrefix();
    }

    /**
     * 获取数据作用域
     *
     * @return
     */
    private String getDataScope() {
        return StringUtils.isNotBlank(dataScope) ? dataScope + "_" : "";
    }

    /**
     * 获取索引信息
     *
     * @return
     */
    public String getType() {
        return "";
    }

    ;

    /**
     * 获取索引分割的时间格式
     */
    public String getDateFormatStr() {
        return DEFAULT_DATE_FORMAT;
    }

    /**
     * 启动时初始化，之后一分钟同步一次索引
     *
     * <AUTHOR>
     * @date 2020年5月14日
     */
//    @Scheduled(cron = "0 */1 * * * ?")
    @PostConstruct
    protected void cacheIndex() {
        List<String> indices = getIndicesByPrefix(getFullIndexPrefix());
        String index = getIndexByTime(System.currentTimeMillis());
        // 最新的索引可能在更新缓存时没被创建，在此创建，防止可能的在1分钟内查询不到最新索引数据的问题
        if (!indices.contains(index)) {
            initIndexByName(index);
            indices.add(index);
        }
        INDICES_MAP.put(getFullIndexPrefix(), indices);
    }

    /**
     * 根据时间返回相应索引
     *
     * @param timestamp
     * @return
     * <AUTHOR>
     * @date 2020年5月14日
     */
    protected String getIndexByTime(long timestamp) {
        Date date = new Date();
        date.setTime(timestamp);
        LocalDateTime localDateTime = Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault()).toLocalDateTime();
        return getFullIndexPrefix() + localDateTime.format(DateTimeFormatter.ofPattern(getDateFormatStr()));
    }

    /**
     * 根据时间查询缓存中是否有相应索引，没有就创建
     *
     * @param timestamp
     * @return
     * <AUTHOR>
     * @date 2020年5月14日
     */
    protected String initIndexByTime(long timestamp) {
        String index = getIndexByTime(timestamp);
        List<String> list = getCacheIndices();
        if (list != null && list.contains(index)) {
            return index;
        }
        initIndex(index);
        return index;
    }

    /**
     * 根据索引名查询缓存中是否有相应索引，没有就创建
     *
     * @param index
     * @return
     * <AUTHOR>
     * @date 2020年5月14日
     */
    protected void initIndexByName(String index) {
        List<String> list = getCacheIndices();
        if (list != null && list.contains(index)) {
            return;
        }
        initIndex(index);
        return;
    }

    /**
     * 自定义type, 子类重写{@code getType()}方法指定固定type
     *
     * @param index index array
     */
    private String[] getIndexType(String... index) {
        String type = getType();
        return "".equals(type) ? index : type.split(",");
    }

    /**
     * 初始化索引
     *
     * @param index
     * @author: weixuesong
     * @date: 2020/9/3 14:39
     * @return: void
     */
    protected void initIndex(String index) {
        try {
            RestHighLevelClient restHighLevelClient = getClient();
            GetIndexRequest getCi = new GetIndexRequest(index);
            boolean existCi = restHighLevelClient.indices().exists(getCi, RequestOptions.DEFAULT);
            if (!existCi) {
                CreateIndexRequest createIndex = new CreateIndexRequest(index);
                settings(createIndex);
                // ES 7.x兼容性修复：移除废弃的type参数，直接使用mapping
                createIndex.mapping(getSelfMapping(index));
                restHighLevelClient.indices().create(createIndex, RequestOptions.DEFAULT);
                HashMap<String, Object> map = new HashMap<>(2);
                map.put("index.mapping.total_fields.limit", 20000);
                map.put("index.max_result_window", 100000);
                UpdateSettingsRequest updateSettingsRequest = new UpdateSettingsRequest(index);
                updateSettingsRequest.settings(map);
                restHighLevelClient.indices().putSettings(updateSettingsRequest, RequestOptions.DEFAULT);
            }
        } catch (Exception e) {
            log.error("init index mapping config error :: index== [" + index + "] :: type== [" + index + "]  " + e.getMessage(), e);
        }
    }

    /**
     * 设置setting
     *
     * @param createIndex
     * @author: weixuesong
     * @date: 2020/9/3 14:49
     * @return: void
     */
    protected void settings(CreateIndexRequest createIndex) {
        XContentBuilder setting;
        try {
            setting = XContentFactory.jsonBuilder();
            setting.startObject()
                    // 设置索引字段数限制和最大结果窗口
                    .field("index.mapping.total_fields.limit", 20000)
                    .field("index.max_result_window", 100000)
                    .startObject("analysis")
                    .startObject("normalizer")
                    .startObject("my_normalizer")
                    .field("type", "custom")
                    .field("char_filter", new ArrayList<>())
                    .field("filter", Collections.singletonList("lowercase"))
                    .endObject()
                    .endObject()
                    .endObject()
                    .endObject();
            createIndex.settings(setting);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
    }

    /**
     * 获取简化的mapping配置 - ES 7.x兼容版本
     * 用于不需要动态模板的简单场景
     *
     * @return XContentBuilder mapping配置
     */
    public XContentBuilder getSelfMapping() {
        XContentBuilder mapping = null;
        try {
            mapping = XContentFactory.jsonBuilder();
            mapping.startObject() // 根对象，ES 7.x不需要额外的包装
                    .startObject("properties")
                    .startObject("time").field("type", "date").endObject()
                    .startObject("value").field("type", "double").endObject()
                    .endObject()
                    .field("date_detection", false) // 禁用日期自动检测
                    .endObject();
        } catch (Exception e) {
            log.error("构建简化 Mapping 出错: " + e.getMessage(), e);
        }
        return mapping;
    }

    /**
     * 获取mapping配置 - ES 7.x兼容版本
     * 参考AbstractESBaseDao的getMapping方法，确保动态模板正确处理各种数据类型
     *
     * @param index 索引名称
     * @author: weixuesong
     * @date: 2020/9/3 14:40
     * @return: org.elasticsearch.common.xcontent.XContentBuilder
     */
    protected XContentBuilder getSelfMapping(String index) {
        XContentBuilder mapping = null;
        try {
            mapping = XContentFactory.jsonBuilder();
            mapping.startObject(); // 根对象，ES 7.x不需要index名称作为根对象

            // 基础属性配置
            mapping.startObject("properties")
                    .startObject("time").field("type", "date").endObject()
                    .startObject("value").field("type", "double").endObject()
                    .endObject();

            // 额外配置
            mapping.field("date_detection", false);

            // 动态模板配置 - 参考AbstractESBaseDao，确保正确处理各种数据类型
            mapping.startArray("dynamic_templates");

            // 1. 处理特殊字符串字段（name、code、key等）
            mapping.startObject().startObject("string_code_stdkeyword")
                    .field("match_mapping_type", "string")
                    .field("match_pattern", "regex")
                    .field("match", ".*?name|.*?code|.*?key|.*?Name|.*?Code|.*?Key")
                    .startObject("mapping")
                    .field("analyzer", "my-analyzer")
                    .field("type", "text")
                    .startObject("fields")
                    .startObject("keyword").field("ignore_above", 256).field("type", "keyword").endObject()
                    .startObject("stdkeyword").field("ignore_above", 256).field("type", "keyword").field("normalizer", "my_normalizer").endObject()
                    .endObject()
                    .endObject()
                    .endObject().endObject();

            // 2. 处理一般字符串字段
            mapping.startObject().startObject("strings")
                    .field("match_mapping_type", "string")
                    .startObject("mapping")
                    .field("analyzer", "my-analyzer")
                    .field("type", "text")
                    .startObject("fields")
                    .startObject("keyword").field("ignore_above", 256).field("type", "keyword").endObject()
                    .endObject()
                    .endObject()
                    .endObject().endObject();

            // 3. 处理long类型字段 - 确保不添加analyzer参数
            mapping.startObject().startObject("longs")
                    .field("match_mapping_type", "long")
                    .startObject("mapping")
                    .field("type", "long")
                    .endObject()
                    .endObject().endObject();

            // 4. 处理double类型字段 - 确保不添加analyzer参数
            mapping.startObject().startObject("doubles")
                    .field("match_mapping_type", "double")
                    .startObject("mapping")
                    .field("type", "double")
                    .endObject()
                    .endObject().endObject();

            mapping.endArray(); // dynamic_templates
            mapping.endObject(); // root object

        } catch (Exception e) {
            log.error("构建 Mapping 出错: " + e.getMessage(), e);
        }
        return mapping;
    }


    /**
     * 获取客户端链接
     *
     * @author: weixuesong
     * @date: 2020/9/3 14:41
     * @return: org.elasticsearch.client.RestHighLevelClient
     */
    public RestHighLevelClient getClient() {
        return client;
    }

    private Page<JSONObject> resultTransPage(long pageNum, long pageSize, long totalCount, List<JSONObject> arr) {
        Page<JSONObject> page = new Page<JSONObject>();
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        if (totalCount == 0) {
            page.setTotalRows(totalCount);
            page.setTotalPages(1);
            page.setData(arr);
            return page;
        }
        long totalPages = totalCount % pageSize;
        if (totalPages == 0) {
            page.setTotalPages(totalCount / pageSize);
        } else {
            page.setTotalPages(totalCount / pageSize + 1);
        }
        page.setTotalRows(totalCount);
        page.setData(arr);
        return page;
    }

    protected void fillInfo(JSONObject obj) {
        SysUser currentUser = null;
        try {
            currentUser = SysUtil.getCurrentUserInfo();
        } catch (LoginException e) {
            log.debug("无法获取当前登陆用户，该持久化信息可能会缺失[创建/修改]人信息和domain域信息不对的问题");
        }
        fillInfo(obj, currentUser);
    }

    /**
     * 填补持久化信息，如创建/修改人/domainId/id/创建/修改时间
     *
     * @param obj
     */
    protected void fillInfo(JSONObject obj, SysUser currentUser) {
        if (obj == null) {
            return;
        }
        String userLoginCode = currentUser == null ? "system" : currentUser.getLoginCode();
        Long nowTime = ESUtil.getNumberDateTime();
        if (!obj.containsKey("id") || BinaryUtils.isEmpty(obj.get("id"))) {
            // add-data处理
            obj.put("id", ESUtil.getUUID());
            obj.put("createTime", nowTime);
            if (!obj.containsKey("creator") || BinaryUtils.isEmpty(obj.get("creator"))) {
                obj.put("creator", userLoginCode);
            }
        } else {
            // update-data处理
            if (!obj.containsKey("createTime") || BinaryUtils.isEmpty(obj.get("createTime"))) {
                obj.put("createTime", nowTime);
            }
        }
        obj.put("modifyTime", nowTime);
        obj.put("modifier", userLoginCode);
        if (!obj.containsKey("domainId") || BinaryUtils.isEmpty(obj.get("domainId"))) {
            obj.put("domainId", currentUser != null ? currentUser.getDomainId() : 1L);
        }
    }


    /**
     * 异步批量新增
     *
     * @param list 对象组
     * @return
     */
    public void asyncSaveBatch(JSONArray list, String index) {
        initIndexByName(index);
        BulkRequest bulkRequest = new BulkRequest();
        bulkRequest.timeout(TimeValue.timeValueMinutes(2));
        if (null != list && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                JSONObject obj = list.getJSONObject(i);
                fillInfo(obj);
                IndexRequest indexRequest = this.getIndexRequest(index, obj.get("id").toString());
                indexRequest.source(obj);
                bulkRequest.add(indexRequest);
            }

            getClient().bulkAsync(bulkRequest, RequestOptions.DEFAULT, new ActionListener<BulkResponse>() {
                @Override
                public void onResponse(BulkResponse bulkItemResponses) {
                    boolean hasFailures = bulkItemResponses.hasFailures();
                    if (hasFailures) {
                        log.error(">>> bulkAsync save document has failures:{}", bulkItemResponses.buildFailureMessage());
                    }
                }

                @Override
                public void onFailure(Exception e) {
                    log.error(">>> save document error", e);
                }
            });
        }
    }

    /**
     * 同步步批量新增
     *
     * @param list 对象组
     * @return
     */
    public boolean syncSaveBatch(JSONArray list, String index) {
        initIndexByName(index);
        BulkRequest bulkRequest = new BulkRequest();
        bulkRequest.timeout(TimeValue.timeValueMinutes(2));
        boolean flag = true;
        if (null != list && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                JSONObject obj = list.getJSONObject(i);
                fillInfo(obj);
                IndexRequest indexRequest = this.getIndexRequest(index, obj.get("id").toString());
                indexRequest.source(obj);
                bulkRequest.add(indexRequest);
            }

            try {
                BulkResponse bulkResponse = getClient().bulk(bulkRequest, RequestOptions.DEFAULT);
                if (bulkResponse.hasFailures()) {
                    flag = false;
                    log.error(bulkResponse.buildFailureMessage());
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
                throw new MessageException(e.getMessage());
            }
        }
        return flag;
    }


    /**
     * 保存或更新对象
     *
     * @param obj 对象
     * @return
     */
    public boolean saveOrUpdate(JSONObject obj, String index) {
        initIndexByName(index);
        boolean flag = false;
        fillInfo(obj);
        UpdateRequest uRequest = this.getUpdateRequest(index, obj.get("id").toString());
        uRequest.doc(obj);
        uRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        uRequest.docAsUpsert(true);
        UpdateResponse upReponse;
        try {
            upReponse = getClient().update(uRequest, RequestOptions.DEFAULT);
            if (upReponse.status() == RestStatus.CREATED) {
                flag = true;
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return flag;
    }

    /**
     * 批量保存或更新
     *
     * @param list 对象组
     * @return
     */
    public boolean saveOrUpdateBatch(JSONArray list, String index) {
        initIndexByName(index);
        boolean flag = true;
        BulkRequest bulkRequest = new BulkRequest();
        for (int i = 0; (!list.isEmpty() && i < list.size()); i++) {
            JSONObject obj = list.getJSONObject(i);
            fillInfo(obj);
            UpdateRequest uRequest = this.getUpdateRequest(index, obj.get("id").toString());
            uRequest.doc(obj);
            uRequest.docAsUpsert(true);
            bulkRequest.add(uRequest);
        }
        bulkRequest.timeout(TimeValue.timeValueMinutes(2));
        bulkRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        try {
            BulkResponse bulkResponse = getClient().bulk(bulkRequest, RequestOptions.DEFAULT);
            if (bulkResponse.hasFailures()) {
                flag = false;
                log.info(bulkResponse.buildFailureMessage());
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return flag;
    }

    /**
     * 异步批量保存或更新
     *
     * @param list 对象组
     * @return
     */
    public void asyncSaveOrUpdateBatch(JSONArray list, String index) {
        initIndexByName(index);
        BulkRequest bulkRequest = new BulkRequest();
        bulkRequest.timeout(TimeValue.timeValueMinutes(2));
        if (null != list && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                JSONObject obj = list.getJSONObject(i);
                fillInfo(obj);
                UpdateRequest uRequest =this.getUpdateRequest(index, obj.get("id").toString());
                uRequest.doc(obj);
                uRequest.docAsUpsert(true);
                bulkRequest.add(uRequest);
            }
            getClient().bulkAsync(bulkRequest, RequestOptions.DEFAULT, new ActionListener<BulkResponse>() {
                @Override
                public void onResponse(BulkResponse bulkItemResponses) {

                    boolean hasFailures = bulkItemResponses.hasFailures();
                    if (hasFailures) {
                        log.error(">>> bulkAsync save document has failures:{}", bulkItemResponses.buildFailureMessage());
                    }
                }

                @Override
                public void onFailure(Exception e) {
                    log.error(">>> save document error", e);
                }
            });
        }
    }

    public boolean saveOrUpdateNoRefresh(JSONObject obj, long timestamp) {
        boolean flag = false;
        String index = initIndexByTime(timestamp);
        fillInfo(obj);
        UpdateRequest uRequest = this.getUpdateRequest(index, obj.get("id").toString());
        uRequest.doc(obj);
        uRequest.docAsUpsert(true);
        UpdateResponse upReponse = null;
        try {
            upReponse = getClient().update(uRequest, RequestOptions.DEFAULT);
            if (upReponse.status() == RestStatus.CREATED) {
                flag = true;
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return flag;
    }


    /**
     * 批量保存或更新不立即刷新
     *
     * @param list 对象组
     * @return
     */
    public boolean saveOrUpdateBatchNoRefresh(JSONArray list, String index) {
        initIndexByName(index);
        boolean flag = true;
        BulkRequest bulkRequest = new BulkRequest();
        for (int i = 0; (!list.isEmpty() && i < list.size()); i++) {
            JSONObject obj = list.getJSONObject(i);
            fillInfo(obj);
            UpdateRequest uRequest = this.getUpdateRequest(index, obj.get("id").toString());
            uRequest.doc(obj);
            uRequest.docAsUpsert(true);
            bulkRequest.add(uRequest);
        }
        bulkRequest.timeout(TimeValue.timeValueMinutes(2));
        try {
            BulkResponse bulkResponse = getClient().bulk(bulkRequest, RequestOptions.DEFAULT);
            if (bulkResponse.hasFailures()) {
                flag = false;
                log.info(bulkResponse.buildFailureMessage());
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return flag;
    }

    /**
     * 排序分页获取对象
     *
     * @param pageNum   页码
     * @param pageSize  页大小
     * @param query     查询条件
     * @param sorts     排序对象
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    public Page<JSONObject> getSortListByQuery(int pageNum, int pageSize, QueryBuilder query, List<SortBuilder<?>> sorts, Long startTime, Long endTime) {
        // 根据起止时间获取相应索引
        String[] indicesArr = getIndicesArrayByTime(startTime, endTime);
        Page<JSONObject> page = new Page<JSONObject>();
        if (indicesArr.length == 0) {
            return page;
        }
        List<JSONObject> rs = new ArrayList<JSONObject>();
        SearchRequest searchRequest = this.getSearchRequest(indicesArr);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        int from = (pageNum - 1) * pageSize < 0 ? 0 : (pageNum - 1) * pageSize;
        sourceBuilder.from(from);
        sourceBuilder.size(pageSize);
        sourceBuilder.query(query);
        for (SortBuilder<?> sort : sorts) {
            sourceBuilder.sort(sort);
        }
        searchRequest.source(sourceBuilder);
        log.debug(searchRequest.toString());
        long totalCount = 0L;
        try {
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            totalCount = response.getHits().getTotalHits().value;
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                JSONObject result = JSON.parseObject(recordStr);
                rs.add(result);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return resultTransPage(pageNum, pageSize, totalCount, rs);
    }

    /**
     * 排序分页获取对象
     *
     * @param pageNum  页码
     * @param pageSize 页大小
     * @param query    查询条件
     * @param sorts    排序对象
     * @return
     */
    public Page<JSONObject> getSortListByQuery(int pageNum, int pageSize, QueryBuilder query, List<SortBuilder<?>> sorts) {
        List<JSONObject> rs = new ArrayList<JSONObject>();
        // 查询所有缓存的索引
        String[] indicesArray = getAndCheckCacheIndicesArray();
        SearchRequest searchRequest = this.getSearchRequest(indicesArray);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        int from = (pageNum - 1) * pageSize < 0 ? 0 : (pageNum - 1) * pageSize;
        sourceBuilder.from(from);
        sourceBuilder.size(pageSize);
        sourceBuilder.query(query);
        for (SortBuilder<?> sort : sorts) {
            sourceBuilder.sort(sort);
        }
        searchRequest.source(sourceBuilder);
        log.debug(searchRequest.toString());
        long totalCount = 0L;
        try {
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            totalCount = response.getHits().getTotalHits().value;
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                JSONObject result = JSON.parseObject(recordStr);
                rs.add(result);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return resultTransPage(pageNum, pageSize, totalCount, rs);
    }

    /**
     * 分页获取对象
     *
     * @param pageNum   页码
     * @param pageSize  页大小
     * @param query     查询条件
     * @param sortField 排序字段
     * @param isAsc     是否升序
     * @return
     */
    public Page<JSONObject> getSortListByQuery(int pageNum, int pageSize, QueryBuilder query, String sortField, boolean isAsc) {
        List<JSONObject> rs = new ArrayList<JSONObject>();
        SortOrder orderType = isAsc ? SortOrder.ASC : SortOrder.DESC;
        String[] indicesArray = getAndCheckCacheIndicesArray();
        SearchRequest searchRequest = this.getSearchRequest(indicesArray);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        int from = (pageNum - 1) * pageSize < 0 ? 0 : (pageNum - 1) * pageSize;
        sourceBuilder.from(from);
        sourceBuilder.size(pageSize);
        sourceBuilder.query(query);
        sourceBuilder.sort(SortBuilders.fieldSort(sortField).order(orderType));
        searchRequest.source(sourceBuilder);
        long totalCount = 0L;
        try {
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            totalCount = response.getHits().getTotalHits().value;
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                JSONObject result = JSON.parseObject(recordStr);
                rs.add(result);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return resultTransPage(pageNum, pageSize, totalCount, rs);
    }

    /**
     * 分页获取对象
     *
     * @param pageNum   页码
     * @param pageSize  页大小
     * @param query     查询条件
     * @param sortField 排序字段，降序
     * @return
     */
    public Page<JSONObject> getSortListByQuery(int pageNum, int pageSize, QueryBuilder query, String sortField, Long startTime, Long endTime, boolean isAsc) {
        // 根据起止时间获取相应索引
        String[] indices = getIndicesArrayByTime(startTime, endTime);
        Page<JSONObject> page = new Page<JSONObject>();
        if (BinaryUtils.isEmpty(indices)) {
            return page;
        }
        SortOrder orderType = isAsc ? SortOrder.ASC : SortOrder.DESC;
        List<JSONObject> rs = new ArrayList<JSONObject>();
        SearchRequest searchRequest = this.getSearchRequest(indices);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        int from = (pageNum - 1) * pageSize < 0 ? 0 : (pageNum - 1) * pageSize;
        sourceBuilder.from(from);
        sourceBuilder.size(pageSize);
        sourceBuilder.query(query);
        sourceBuilder.sort(SortBuilders.fieldSort(sortField).order(orderType));
        searchRequest.source(sourceBuilder);
        long totalCount = 0L;
        try {
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            totalCount = response.getHits().getTotalHits().value;
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                JSONObject result = JSON.parseObject(recordStr);
                rs.add(result);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return resultTransPage(pageNum, pageSize, totalCount, rs);
    }

    /**
     * 分页获取对象
     *
     * @param pageNum  页码
     * @param pageSize 页大小
     * @param query    查询条件
     * @return
     */
    public Page<JSONObject> getListByQuery(int pageNum, int pageSize, QueryBuilder query) {
        List<JSONObject> rs = new ArrayList<JSONObject>();
        // 查询所有缓存的索引
        String[] indicesArray = getAndCheckCacheIndicesArray();
        SearchRequest searchRequest = this.getSearchRequest(indicesArray);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        int from = (pageNum - 1) * pageSize < 0 ? 0 : (pageNum - 1) * pageSize;
        sourceBuilder.from(from);
        sourceBuilder.size(pageSize);
        sourceBuilder.query(query);
        searchRequest.source(sourceBuilder);
        long totalCount = 0L;
        try {
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            totalCount = response.getHits().getTotalHits().value;
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                JSONObject result = JSON.parseObject(recordStr);
                rs.add(result);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return resultTransPage(pageNum, pageSize, totalCount, rs);
    }

    /**
     * 查询更新
     *
     * @param query     查询条件
     * @param scriptStr 查询的脚本,格式：ctx._source.文档字段=值 simple:
     *                  多个属性更新："ctx._source.attrs['应用负责人ID']='cgjabc';ctx._source.attrs['b']+=1"
     *                  子文档： ctx._source.attrs['应用负责人ID']='abc' 文档：
     *                  ctx._source.name='abc'
     * @param index     查询索引
     * @return
     */
    public boolean updateByQuery(QueryBuilder query, String scriptStr, String index) {
        boolean flag = true;
        RestClient lowClient = getClient().getLowLevelClient();
        Map<String, Object> scriptMap = new HashMap<String, Object>();
        scriptMap.put("source", scriptStr);
        scriptMap.put("lang", "painless");
        String param = "{\"query\":" + query.toString() + ",\"script\":" + JSON.toJSONString(scriptMap) + "}";
        HttpEntity entity = new NStringEntity(param, ContentType.APPLICATION_JSON);
        try {
            log.info(EntityUtils.toString(entity));
            String url = "/" + index + "/_update_by_query?conflicts=proceed&refresh=true";
            Request request = new Request("POST", url);
            request.setEntity(entity);
            Response res = lowClient.performRequest(request);
            if (res.getStatusLine().getStatusCode() != 200) {
                flag = false;
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            flag = false;
            throw new MessageException(e.getMessage());
        }
        return flag;
    }

    /**
     * 查询更新
     *
     * @param query     查询条件
     * @param scriptStr 查询的脚本,格式：ctx._source.文档字段=值 simple:
     *                  多个属性更新："ctx._source.attrs['应用负责人ID']='cgjabc';ctx._source.attrs['b']+=1"
     *                  子文档： ctx._source.attrs['应用负责人ID']='abc' 文档：
     *                  ctx._source.name='abc'
     * @return
     */
    public boolean updateByQuery(QueryBuilder query, String scriptStr) {
        List<String> cacheIndices = getAndCheckCacheIndices();
        String indices = StringUtils.join(cacheIndices, ",");
        return this.updateByQuery(query, scriptStr, indices);
    }

    /**
     * 根据前缀获取索引
     *
     * @param prefix
     * @return
     * @throws IOException
     * <AUTHOR>
     * @date 2019年9月24日 下午2:19:36
     */
    public List<String> getIndicesByPrefix(String prefix) {
        List<String> indices = new ArrayList<>();
        GetAliasesRequest getAliasesRequest = new GetAliasesRequest();
        GetAliasesResponse res;
        try {
            res = getClient().indices().getAlias(getAliasesRequest, RequestOptions.DEFAULT);
            Map<String, Set<AliasMetadata>> map = res.getAliases();
            Iterator<String> it = map.keySet().iterator();
            while (it.hasNext()) {
                String index = it.next();
                if (prefix != null && index.startsWith(prefix)) {
                    indices.add(index);
                } else if (prefix == null) {
                    indices.add(index);
                }
            }
        } catch (IOException e) {
            log.error("getIndicesByPrefix error", e);
        }
        return indices;
    }

    public String[] getIndicesArrayByTime(Long startTime, Long endTime) {
        return getIndicesByTime(startTime, endTime).toArray(new String[0]);
    }

    /**
     * 根据时间获取需要查询的索引,空则抛异常
     *
     * @param startTime
     * @param endTime
     * @author: weixuesong
     * @date: 2020/5/20 15:02
     * @return: java.util.List<java.lang.String>
     */
    protected List<String> getIndicesByTime(Long startTime, Long endTime) {
        if (startTime == null || endTime == null) {
            return getIndicesByPrefix(getFullIndexPrefix());
        }
        if (startTime > endTime) {
            throw new RuntimeException("getIndicesByTime error: startTime > endTime");
        }
        String indexPrefix = getFullIndexPrefix();
        List<String> cacheIndices = getAndCheckCacheIndices();
        if (BinaryUtils.isEmpty(cacheIndices)) {
            return new ArrayList<>();
        }
        SimpleDateFormat sdf = new SimpleDateFormat(getDateFormatStr());
        List<String> indices = new ArrayList<>();

        Calendar startCal = Calendar.getInstance();
        startCal.setTimeInMillis(startTime);

        Calendar endCal = Calendar.getInstance();
        endCal.setTimeInMillis(endTime);

        Set<String> indicesSet = new HashSet<>();
        indicesSet.add(indexPrefix + sdf.format(new Date(endCal.getTimeInMillis())));
        // 比较两个时间的年月，不相同就将start时间加一个月
        while (startCal.get(Calendar.YEAR) != endCal.get(Calendar.YEAR) || startCal.get(Calendar.MONTH) != endCal.get(Calendar.MONTH) || startCal.get(Calendar.DAY_OF_MONTH) != endCal.get(Calendar.DAY_OF_MONTH)) {
            endCal.add(Calendar.DAY_OF_MONTH, -1);
            indicesSet.add(indexPrefix + sdf.format(new Date(endCal.getTimeInMillis())));
        }
        // 只返回存在的索引
        for (String index : indicesSet) {
            if (cacheIndices.contains(index)) {
                indices.add(index);
            }
        }
        return indices;
    }

    /**
     * 获取缓存的索引,空则抛异常
     *
     * @author: weixuesong
     * @date: 2020/5/20 15:39
     * @return: java.util.List<java.lang.String>
     */
    protected List<String> getAndCheckCacheIndices() {
        List<String> cacheIndices = getCacheIndices();
        if (BinaryUtils.isEmpty(cacheIndices)) {
            throw new RuntimeException(getFullIndexPrefix() + "cache indices is empty");
        }
        return cacheIndices;
    }

    protected String[] getAndCheckCacheIndicesArray() {
        return getAndCheckCacheIndices().toArray(new String[0]);
    }

    /**
     * 获取缓存的索引,返回可能为空
     *
     * @author: weixuesong
     * @date: 2020/5/20 15:39
     * @return: java.util.List<java.lang.String>
     */
    protected List<String> getCacheIndices() {
        return INDICES_MAP.get(getFullIndexPrefix());
    }

    public SearchRequest getSearchRequest(QueryBuilder query) {
        return getSearchRequest(query, getAndCheckCacheIndicesArray());
    }

    public SearchRequest getSearchRequest(QueryBuilder query, String[] indices) {
        SearchRequest searchRequest = this.getSearchRequest(indices);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.query(query);
        return searchRequest;
    }

    public SearchRequest getSearchRequest(QueryBuilder query, AggregationBuilder agg, String[] indices) {
        SearchRequest searchRequest = this.getSearchRequest(indices);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.query(query);
        searchRequest.source().aggregation(agg);
        return searchRequest;
    }

    /**
     * 获取对象--文档属性id值搜索
     *
     * @param id 文档属性id值搜索
     * @return
     */
    public JSONObject getByID(Long id) {
        JSONObject rs = new JSONObject();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.from(0);
        sourceBuilder.size(1);
        String[] indicesArray = getAndCheckCacheIndicesArray();
        SearchRequest searchRequest = this.getSearchRequest(indicesArray);
        TermQueryBuilder query = QueryBuilders.termQuery("id", id);
        sourceBuilder.query(query);
        searchRequest.source(sourceBuilder);
        try {
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                rs = JSON.parseObject(recordStr);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return rs;
    }

    /**
     * 获取对象--文档属性id值搜索
     *
     * @param id 文档属性id值搜索-- 字符keyword
     * @return
     */
    public JSONObject getByID(String id) {
        JSONObject rs = new JSONObject();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.from(0);
        sourceBuilder.size(1);
        String[] indicesArray = getAndCheckCacheIndicesArray();
        SearchRequest searchRequest = this.getSearchRequest(indicesArray);
        TermQueryBuilder query = QueryBuilders.termQuery("id.keyword", id);
        sourceBuilder.query(query);
        searchRequest.source(sourceBuilder);
        try {
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                rs = JSON.parseObject(recordStr);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);

        }

        return rs;
    }

    public long cardinalityByQuery(QueryBuilder query, String field) {
        RestHighLevelClient client = getClient();
        SearchRequest searchRequest = getSearchRequest(query);
        CardinalityAggregationBuilder count = AggregationBuilders.cardinality("deviceCount").field(field).precisionThreshold(1000);
        searchRequest.source().aggregation(count);
        try {
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            Cardinality valueCount = searchResponse.getAggregations().get("deviceCount");
            return valueCount.getValue();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return 0L;
    }

    public long countByQuery(QueryBuilder query, String field) {
        RestHighLevelClient client = getClient();
        SearchRequest searchRequest = getSearchRequest(query);
        ValueCountAggregationBuilder count = AggregationBuilders.count("count").field(field);
        searchRequest.source().aggregation(count);
        try {
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            ValueCount valueCount = searchResponse.getAggregations().get("count");
            return valueCount.getValue();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return 0L;
    }

    public <T> Map<T, Long> groupByCountFieldNew(String field, QueryBuilder query, int size, Class<T> toType) {
        Map<T, Long> map = new LinkedHashMap<>(size);
        RestHighLevelClient c = getClient();
        SearchRequest searchRequest = getSearchRequest(query);
        TermsAggregationBuilder term = AggregationBuilders.terms("agg").field(field).order(BucketOrder.count(true));
        term.size(size);
        searchRequest.source().aggregation(term);
        try {
            SearchResponse searchResponse = c.search(searchRequest, RequestOptions.DEFAULT);
            Terms agg = searchResponse.getAggregations().get("agg");
            for (Terms.Bucket entry : agg.getBuckets()) {
                T key;
                if (Number.class.isAssignableFrom(toType)) {
                    key = (T) entry.getKeyAsNumber();
                } else {
                    key = (T) entry.getKeyAsString();
                }
                map.put(key, entry.getDocCount());
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return map;
    }

    /**
     * 获取统计结果
     *
     * @param field 统计字段
     * @param query 查询条件
     * @return
     */
    public List<Map<String, Long>> groupByCountField(String field, QueryBuilder query) {
        List<Map<String, Long>> list = new ArrayList<Map<String, Long>>();
        RestHighLevelClient c = getClient();
        String[] indicesArray = getAndCheckCacheIndicesArray();
        SearchRequest searchRequest = this.getSearchRequest(indicesArray);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 添加查询条件
        //
        // 默认是10
        int size = 1000;
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.query(query);
        TermsAggregationBuilder term = AggregationBuilders.terms("field").field("otherFiledMap." + field);
        term.size(size);
        searchSourceBuilder.aggregation(term);
        SearchResponse searchResponse;
        try {
            searchResponse = c.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations agg = searchResponse.getAggregations();
            Terms levelObject = agg.get("field");
            for (Terms.Bucket bucket : levelObject.getBuckets()) {
                Map<String, Long> map = new HashMap<String, Long>();
                String key = bucket.getKeyAsString();
                if (key == null || key.length() <= 0) {
                    key = "";
                }
                map.put(key, bucket.getDocCount());
                list.add(map);
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return list;
    }

    public <T> Map<T, Long> countTopFields(String field, QueryBuilder query, Class<T> toType, int pageNum, int pageSize, List<SortBuilder<?>> sorts) {
        Map<T, Long> map = new LinkedHashMap<>(pageSize);
        RestHighLevelClient c = this.getClient();
        //int from = (pageNum - 1) * pageSize < 0 ? 0 : (pageNum - 1) * pageSize;
        SearchRequest searchRequest = getSearchRequest(query);
        TopHitsAggregationBuilder topHit = AggregationBuilders.topHits("top");
        if (sorts != null) {
            topHit.sorts(sorts);
        }
/*        AggregationBuilder aggTerm =
                AggregationBuilders.terms("agg").size(pageSize).field(field).subAggregation(topHit);*/
        TermsAggregationBuilder aggTerm = AggregationBuilders.terms("agg").field(field).order(BucketOrder.count(false));
        //按分片取TOP
        aggTerm.size(pageSize);
        aggTerm.shardSize(150);
        aggTerm.minDocCount(1L);

        searchRequest.source().aggregation(aggTerm);
        try {
            SearchResponse searchResponse = c.search(searchRequest, RequestOptions.DEFAULT);
            Terms agg = searchResponse.getAggregations().get("agg");
            for (Terms.Bucket entry : agg.getBuckets()) {
                T key;
                if (Number.class.isAssignableFrom(toType)) {
                    key = (T) entry.getKeyAsNumber();
                } else {
                    key = (T) entry.getKeyAsString();
                }
                map.put(key, entry.getDocCount());
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return map;
    }

    public Map<String, List<JSONObject>> groupField(String field, BoolQueryBuilder query) {
        Map<String, List<JSONObject>> map = new LinkedHashMap<>();
        RestHighLevelClient c = this.getClient();
        String[] indicesArray = getAndCheckCacheIndicesArray();
        SearchRequest searchRequest = this.getSearchRequest(indicesArray);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        int size = 1000;
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.query(query);
        List<BucketOrder> orders = new ArrayList<>(2);
        orders.add(BucketOrder.key(true));
        orders.add(BucketOrder.count(true));
        TermsAggregationBuilder term = AggregationBuilders.terms("field").field(field).order(orders);
        TopHitsAggregationBuilder top = AggregationBuilders.topHits("top").size(100).sort("firstOccurrence", SortOrder.DESC);
        term.subAggregation(top);
        term.size(size);
        searchSourceBuilder.aggregation(term);

        try {
            SearchResponse searchResponse = c.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations agg = searchResponse.getAggregations();
            Terms levelObject = agg.get("field");
            for (Terms.Bucket bucket : levelObject.getBuckets()) {
                TopHits topHits = bucket.getAggregations().get("top");
                SearchHit[] hits = topHits.getHits().getHits();
                List<JSONObject> eventList = new ArrayList<>(hits.length);
                map.put(bucket.getKeyAsString(), eventList);
                for (SearchHit hit : topHits.getHits().getHits()) {
                    eventList.add(new JSONObject(hit.getSourceAsMap()));
                }
            }
        } catch (IOException var16) {
            log.error(var16.getMessage(), var16);
        }
        return map;
    }

    public <T> List<T> topFields(String field, QueryBuilder query, Class<T> toType, int size) {
        List<T> list = new ArrayList<>();
        RestHighLevelClient c = getClient();
        SearchRequest searchRequest = getSearchRequest(query);
        AggregationBuilder aggregation =
                AggregationBuilders.terms("agg").size(size).field(field)
                        .subAggregation(AggregationBuilders.topHits("top"));
        searchRequest.source().aggregation(aggregation);
        try {
            SearchResponse searchResponse = c.search(searchRequest, RequestOptions.DEFAULT);
            Terms agg = searchResponse.getAggregations().get("agg");

            for (Terms.Bucket entry : agg.getBuckets()) {
                if (Number.class.isAssignableFrom(toType)) {
                    list.add((T) entry.getKeyAsNumber());
                } else {
                    list.add((T) entry.getKeyAsString());
                }
            }
        } catch (IOException var16) {
            log.error(var16.getMessage(), var16);
        }
        return list;
    }

    public Map<String, Long> dateHistogram(QueryBuilder query, String dateHistogramField, DateHistogramInterval interval, String format, long minDocCount, Object extendedBounds) {
        Map<String, Long> linkedHashMap = new LinkedHashMap();
        RestHighLevelClient client = this.getClient();
        SearchRequest searchRequest = getSearchRequest(query);
        AggregationBuilder his = AggregationBuilders.dateHistogram("date_histogram")
                .field(dateHistogramField)
                .dateHistogramInterval(interval)
                .format(format)
                .minDocCount(minDocCount)
                .timeZone(ZoneId.systemDefault());
        searchRequest.source().aggregation(his);
        try {
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations agg = searchResponse.getAggregations();
            ParsedDateHistogram dateHistogram = agg.get("date_histogram");
            for (Histogram.Bucket bucket : dateHistogram.getBuckets()) {
                linkedHashMap.put(bucket.getKeyAsString(), bucket.getDocCount());
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return linkedHashMap;
    }

    public SearchResponse search(SearchRequest searchRequest) {
        SearchResponse searchResponse = null;
        try {
            searchResponse = getClient().search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new MessageException(e.getMessage());
        }
        return searchResponse;
    }

    /**
     * 获取指标扩展字段名
     *
     * @author: weixuesong
     * @date: 2021/3/23 16:59
     * @return: java.util.Set<java.lang.String>
     */
    public Set<String> getMetricAttrKeys() {
        Set<String> resp = new HashSet<>();
        List<String> indices = INDICES_MAP.get(getFullIndexPrefix());
        if (BinaryUtils.isEmpty(indices)) {
            return resp;
        }
        try {
            StringBuilder sb = new StringBuilder();
            for (String index : indices) {
                sb.append(",").append(index);
            }
            RestClient lowClient = getClient().getLowLevelClient();
            Request request = new Request("GET", sb.toString().replaceFirst(",", "") + "/_mapping");
            Response res = lowClient.performRequest(request);
            String responseBody = EntityUtils.toString(res.getEntity());
            JSONObject resJson = JSONObject.parseObject(responseBody);

            for (String index : indices) {
                JSONObject indexObj = resJson.getJSONObject(index);
                if (indexObj == null || indexObj.isEmpty()) {
                    continue;
                }
                Map<String, Object> innerMap = indexObj.getJSONObject("mappings").getInnerMap();
                for (Map.Entry<String, Object> entry : innerMap.entrySet()) {
                    JSONObject parseObject = JSONObject.parseObject(String.valueOf(entry.getValue()));
                    if (parseObject != null && parseObject.containsKey("properties")) {
                        Set<Map.Entry<String, Object>> properties = parseObject.getJSONObject("properties").entrySet();
                        for (Map.Entry<String, Object> stringObjectEntry : properties) {
                            if (!"metricAttrs".equals(stringObjectEntry.getKey())) {
                                continue;
                            }
                            resp.addAll(((JSONObject) stringObjectEntry.getValue()).getJSONObject("properties").keySet());
                        }
                    }
                }
            }
        } catch (IOException e) {
            log.error("getProperties error", e);
        }
        return resp;
    }

    /**
     * check whether the index is out of date
     *
     * @param indexPrefix index prefix
     */
    public void deleteIndexCheck(String indexPrefix, Long saveDays) {
        List<String> indices = getIndicesByPrefix(getFullIndexPrefix());
        SimpleDateFormat sdf = new SimpleDateFormat(getDateFormatStr());
        long thePastFewDays = LocalDateTime.now().plusDays(-saveDays).toInstant(ZoneOffset.of("+8")).toEpochMilli();

        indices.forEach(index -> {
            try {
                if (index.contains(indexPrefix)) {
                    String indexTimeSuffix = index.substring(index.lastIndexOf("_") + 1);
                    long indexTime = sdf.parse(indexTimeSuffix).getTime();

                    if (indexTime < thePastFewDays) {
                        log.warn(">>> delete index [{}]", index);
                        indexDelete(index);
                    }
                }
            } catch (ParseException | IOException ignored) {
            }
        });
    }

    /**
     * 删除索引
     *
     * @param index index
     * @return boolean
     */
    protected boolean indexDelete(String index) throws IOException {
        try {
            DeleteIndexRequest request = new DeleteIndexRequest(index);
            AcknowledgedResponse ackResponse = getClient().indices().delete(request, RequestOptions.DEFAULT);
            return ackResponse.isAcknowledged();
        } catch (ElasticsearchException e) {
            if (e.status() == RestStatus.NOT_FOUND) {
                return true;
            } else {
                log.error(e.getMessage(), e);
                return false;
            }
        }
    }

    protected AggregationBuilder getDateHistogramAggregationBuilder(String name, String fieldName, String offset, Integer min, String expression, Object extendedBounds) {
        DateTimeZone dateTimeZone = DateTimeZone.forOffsetHours(8);
        ZoneId zoneid = ZoneId.of(dateTimeZone.getID());
        DateHistogramAggregationBuilder dateHistogramAggregationBuilder = AggregationBuilders.dateHistogram(name).field(fieldName);

        if (StringUtils.isNotBlank(offset)) {
            dateHistogramAggregationBuilder.offset(offset);
        }
        dateHistogramAggregationBuilder.dateHistogramInterval(min != null ? DateHistogramInterval.minutes(min) : new DateHistogramInterval(expression));
        return dateHistogramAggregationBuilder.timeZone(zoneid);
    }

    protected IndexRequest getIndexRequest(String index, String id) {
        return new IndexRequest(index).id(id);
    }

    protected UpdateRequest getUpdateRequest(String index, String id) {
        return new UpdateRequest(index, id);
    }

    private SearchRequest getSearchRequest(String[] indicesArr) {
        return new SearchRequest(indicesArr);
    }

    protected Map<String, Double> getStatsAgg(Histogram.Bucket bucket, String name) {
        Stats statsAgg = bucket.getAggregations().get(name);
        Map<String, Double> map = new ConcurrentHashMap<>();
        map.put(STATS_AGG_MAX, statsAgg.getMax());
        map.put(STATS_AGG_MIN, statsAgg.getMin());
        map.put(STATS_AGG_AVG, statsAgg.getAvg());
        map.put(STATS_AGG_SUM, statsAgg.getSum());
        return map;
    }

    protected Map<String, Double> getStatsAgg(Terms.Bucket bucket, String name) {
        Stats statsAgg = bucket.getAggregations().get(name);
        Map<String, Double> map = new ConcurrentHashMap<>();
        map.put(STATS_AGG_MAX, statsAgg.getMax());
        map.put(STATS_AGG_MIN, statsAgg.getMin());
        map.put(STATS_AGG_AVG, statsAgg.getAvg());
        map.put(STATS_AGG_SUM, statsAgg.getSum());
        return map;
    }
}
